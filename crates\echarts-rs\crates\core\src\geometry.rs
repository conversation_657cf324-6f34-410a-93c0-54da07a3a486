//! Geometric types and utilities

use serde::{Deserialize, Serialize};

/// 2D point
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ialEq, Serialize, Deserialize)]
pub struct Point {
    pub x: f64,
    pub y: f64,
}

impl Point {
    pub fn new(x: f64, y: f64) -> Self {
        Self { x, y }
    }

    pub fn zero() -> Self {
        Self::new(0.0, 0.0)
    }

    pub fn distance_to(&self, other: Point) -> f64 {
        ((self.x - other.x).powi(2) + (self.y - other.y).powi(2)).sqrt()
    }
}

impl From<(f64, f64)> for Point {
    fn from((x, y): (f64, f64)) -> Self {
        Self::new(x, y)
    }
}

/// 2D size
#[derive(Debu<PERSON>, <PERSON><PERSON>, <PERSON>py, PartialEq, Serialize, Deserialize)]
pub struct Size {
    pub width: f64,
    pub height: f64,
}

impl Size {
    pub fn new(width: f64, height: f64) -> Self {
        Self { width, height }
    }

    pub fn zero() -> Self {
        Self::new(0.0, 0.0)
    }

    pub fn area(&self) -> f64 {
        self.width * self.height
    }
}

impl From<(f64, f64)> for Size {
    fn from((width, height): (f64, f64)) -> Self {
        Self::new(width, height)
    }
}

/// Rectangle bounds
#[derive(Debug, Clone, Copy, PartialEq, Serialize, Deserialize)]
pub struct Bounds {
    pub origin: Point,
    pub size: Size,
}

impl Bounds {
    pub fn new(x: f64, y: f64, width: f64, height: f64) -> Self {
        Self {
            origin: Point::new(x, y),
            size: Size::new(width, height),
        }
    }

    pub fn from_points(top_left: Point, bottom_right: Point) -> Self {
        Self {
            origin: top_left,
            size: Size::new(bottom_right.x - top_left.x, bottom_right.y - top_left.y),
        }
    }

    /// Create bounds from two points (convenience method)
    pub fn from_two_points(p1: Point, p2: Point) -> Self {
        let min_x = p1.x.min(p2.x);
        let min_y = p1.y.min(p2.y);
        let max_x = p1.x.max(p2.x);
        let max_y = p1.y.max(p2.y);

        Self {
            origin: Point::new(min_x, min_y),
            size: Size::new(max_x - min_x, max_y - min_y),
        }
    }

    pub fn zero() -> Self {
        Self {
            origin: Point::zero(),
            size: Size::zero(),
        }
    }

    pub fn left(&self) -> f64 {
        self.origin.x
    }

    pub fn right(&self) -> f64 {
        self.origin.x + self.size.width
    }

    pub fn top(&self) -> f64 {
        self.origin.y
    }

    pub fn bottom(&self) -> f64 {
        self.origin.y + self.size.height
    }

    pub fn center(&self) -> Point {
        Point::new(
            self.origin.x + self.size.width / 2.0,
            self.origin.y + self.size.height / 2.0,
        )
    }

    pub fn min(&self) -> Point {
        self.origin
    }

    pub fn max(&self) -> Point {
        Point::new(self.right(), self.bottom())
    }

    pub fn contains_point(&self, point: Point) -> bool {
        point.x >= self.left()
            && point.x <= self.right()
            && point.y >= self.top()
            && point.y <= self.bottom()
    }

    pub fn intersects(&self, other: Bounds) -> bool {
        self.left() < other.right()
            && self.right() > other.left()
            && self.top() < other.bottom()
            && self.bottom() > other.top()
    }

    pub fn union(&self, other: Bounds) -> Bounds {
        let left = self.left().min(other.left());
        let top = self.top().min(other.top());
        let right = self.right().max(other.right());
        let bottom = self.bottom().max(other.bottom());

        Bounds::new(left, top, right - left, bottom - top)
    }

    pub fn intersect(&self, other: Bounds) -> Bounds {
        let left = self.left().max(other.left());
        let top = self.top().max(other.top());
        let right = self.right().min(other.right());
        let bottom = self.bottom().min(other.bottom());

        if left < right && top < bottom {
            Bounds::new(left, top, right - left, bottom - top)
        } else {
            Bounds::zero()
        }
    }

    pub fn width(&self) -> f64 {
        self.size.width
    }

    pub fn height(&self) -> f64 {
        self.size.height
    }

    pub fn inset(&self, insets: Insets) -> Bounds {
        Bounds::new(
            self.origin.x + insets.left,
            self.origin.y + insets.top,
            self.size.width - insets.left - insets.right,
            self.size.height - insets.top - insets.bottom,
        )
    }
}

// ============================================================================
// GPUI 集成 - Bounds 类型转换
// ============================================================================

#[cfg(feature = "gpui")]
mod gpui_integration {
    use super::*;

    /// 为 EchartsBounds 实现与 gpui::Bounds<gpui::Pixels> 的转换
    impl From<gpui::Bounds<gpui::Pixels>> for Bounds {
        fn from(gpui_bounds: gpui::Bounds<gpui::Pixels>) -> Self {
            Bounds::new(
                gpui_bounds.origin.x.0 as f64,
                gpui_bounds.origin.y.0 as f64,
                gpui_bounds.size.width.0 as f64,
                gpui_bounds.size.height.0 as f64,
            )
        }
    }

    /// 为 EchartsBounds 实现到 gpui::Bounds<gpui::Pixels> 的转换
    impl From<Bounds> for gpui::Bounds<gpui::Pixels> {
        fn from(echarts_bounds: Bounds) -> Self {
            gpui::Bounds::new(
                gpui::point(
                    gpui::px(echarts_bounds.origin.x as f32),
                    gpui::px(echarts_bounds.origin.y as f32)
                ),
                gpui::size(
                    gpui::px(echarts_bounds.size.width as f32),
                    gpui::px(echarts_bounds.size.height as f32)
                )
            )
        }
    }

    /// 为 Point 实现与 gpui::Point<gpui::Pixels> 的转换
    impl From<gpui::Point<gpui::Pixels>> for Point {
        fn from(gpui_point: gpui::Point<gpui::Pixels>) -> Self {
            Point::new(
                gpui_point.x.0 as f64,
                gpui_point.y.0 as f64,
            )
        }
    }

    /// 为 Point 实现到 gpui::Point<gpui::Pixels> 的转换
    impl From<Point> for gpui::Point<gpui::Pixels> {
        fn from(echarts_point: Point) -> Self {
            gpui::point(
                gpui::px(echarts_point.x as f32),
                gpui::px(echarts_point.y as f32)
            )
        }
    }

    /// 为 Size 实现与 gpui::Size<gpui::Pixels> 的转换
    impl From<gpui::Size<gpui::Pixels>> for Size {
        fn from(gpui_size: gpui::Size<gpui::Pixels>) -> Self {
            Size::new(
                gpui_size.width.0 as f64,
                gpui_size.height.0 as f64,
            )
        }
    }

    /// 为 Size 实现到 gpui::Size<gpui::Pixels> 的转换
    impl From<Size> for gpui::Size<gpui::Pixels> {
        fn from(echarts_size: Size) -> Self {
            gpui::size(
                gpui::px(echarts_size.width as f32),
                gpui::px(echarts_size.height as f32)
            )
        }
    }
}

#[cfg(feature = "gpui")]
pub use gpui_integration::*;

/// Insets for padding/margins
#[derive(Debug, Clone, Copy, PartialEq, Serialize, Deserialize)]
pub struct Insets {
    pub top: f64,
    pub right: f64,
    pub bottom: f64,
    pub left: f64,
}

impl Insets {
    pub fn new(top: f64, right: f64, bottom: f64, left: f64) -> Self {
        Self {
            top,
            right,
            bottom,
            left,
        }
    }

    pub fn uniform(value: f64) -> Self {
        Self::new(value, value, value, value)
    }

    pub fn zero() -> Self {
        Self::uniform(0.0)
    }

    pub fn horizontal(value: f64) -> Self {
        Self::new(0.0, value, 0.0, value)
    }

    pub fn vertical(value: f64) -> Self {
        Self::new(value, 0.0, value, 0.0)
    }
}

/// Transform matrix for 2D transformations
#[derive(Debug, Clone, Copy, PartialEq, Serialize, Deserialize)]
pub struct Transform {
    pub a: f64, // scale x
    pub b: f64, // skew y
    pub c: f64, // skew x
    pub d: f64, // scale y
    pub e: f64, // translate x
    pub f: f64, // translate y
}

impl Transform {
    pub fn identity() -> Self {
        Self {
            a: 1.0,
            b: 0.0,
            c: 0.0,
            d: 1.0,
            e: 0.0,
            f: 0.0,
        }
    }

    pub fn translate(x: f64, y: f64) -> Self {
        Self {
            a: 1.0,
            b: 0.0,
            c: 0.0,
            d: 1.0,
            e: x,
            f: y,
        }
    }

    pub fn scale(sx: f64, sy: f64) -> Self {
        Self {
            a: sx,
            b: 0.0,
            c: 0.0,
            d: sy,
            e: 0.0,
            f: 0.0,
        }
    }

    pub fn rotate(angle: f64) -> Self {
        let cos = angle.cos();
        let sin = angle.sin();
        Self {
            a: cos,
            b: sin,
            c: -sin,
            d: cos,
            e: 0.0,
            f: 0.0,
        }
    }

    pub fn multiply(&self, other: Transform) -> Transform {
        Transform {
            a: self.a * other.a + self.b * other.c,
            b: self.a * other.b + self.b * other.d,
            c: self.c * other.a + self.d * other.c,
            d: self.c * other.b + self.d * other.d,
            e: self.e * other.a + self.f * other.c + other.e,
            f: self.e * other.b + self.f * other.d + other.f,
        }
    }

    pub fn then(&self, other: &Transform) -> Transform {
        self.multiply(*other)
    }

    pub fn transform_point(&self, point: Point) -> Point {
        Point::new(
            self.a * point.x + self.c * point.y + self.e,
            self.b * point.x + self.d * point.y + self.f,
        )
    }

    pub fn transform_bounds(&self, bounds: Bounds) -> Bounds {
        let top_left = self.transform_point(bounds.origin);
        let top_right = self.transform_point(Point::new(bounds.right(), bounds.top()));
        let bottom_left = self.transform_point(Point::new(bounds.left(), bounds.bottom()));
        let bottom_right = self.transform_point(Point::new(bounds.right(), bounds.bottom()));

        let min_x = top_left
            .x
            .min(top_right.x)
            .min(bottom_left.x)
            .min(bottom_right.x);
        let max_x = top_left
            .x
            .max(top_right.x)
            .max(bottom_left.x)
            .max(bottom_right.x);
        let min_y = top_left
            .y
            .min(top_right.y)
            .min(bottom_left.y)
            .min(bottom_right.y);
        let max_y = top_left
            .y
            .max(top_right.y)
            .max(bottom_left.y)
            .max(bottom_right.y);

        Bounds::new(min_x, min_y, max_x - min_x, max_y - min_y)
    }

    pub fn inverse(&self) -> Option<Transform> {
        let det = self.a * self.d - self.b * self.c;
        if det.abs() < f64::EPSILON {
            return None;
        }

        let inv_det = 1.0 / det;
        Some(Transform {
            a: self.d * inv_det,
            b: -self.b * inv_det,
            c: -self.c * inv_det,
            d: self.a * inv_det,
            e: (self.c * self.f - self.d * self.e) * inv_det,
            f: (self.b * self.e - self.a * self.f) * inv_det,
        })
    }
}

impl Default for Transform {
    fn default() -> Self {
        Self::identity()
    }
}

/// Path for drawing complex shapes
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Path {
    pub commands: Vec<PathCommand>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum PathCommand {
    MoveTo(Point),
    LineTo(Point),
    QuadraticTo(Point, Point),    // control point, end point
    CubicTo(Point, Point, Point), // control1, control2, end point
    Arc {
        center: Point,
        radius: f64,
        start_angle: f64,
        end_angle: f64,
        clockwise: bool,
    },
    Close,
}

impl Path {
    pub fn new() -> Self {
        Self {
            commands: Vec::new(),
        }
    }

    pub fn move_to(mut self, point: Point) -> Self {
        self.commands.push(PathCommand::MoveTo(point));
        self
    }

    pub fn line_to(mut self, point: Point) -> Self {
        self.commands.push(PathCommand::LineTo(point));
        self
    }

    pub fn quadratic_to(mut self, control: Point, end: Point) -> Self {
        self.commands.push(PathCommand::QuadraticTo(control, end));
        self
    }

    pub fn cubic_to(mut self, control1: Point, control2: Point, end: Point) -> Self {
        self.commands
            .push(PathCommand::CubicTo(control1, control2, end));
        self
    }

    pub fn arc(
        mut self,
        center: Point,
        radius: f64,
        start_angle: f64,
        end_angle: f64,
        clockwise: bool,
    ) -> Self {
        self.commands.push(PathCommand::Arc {
            center,
            radius,
            start_angle,
            end_angle,
            clockwise,
        });
        self
    }

    pub fn close(mut self) -> Self {
        self.commands.push(PathCommand::Close);
        self
    }

    pub fn rect(bounds: Bounds) -> Self {
        Self::new()
            .move_to(bounds.origin)
            .line_to(Point::new(bounds.right(), bounds.top()))
            .line_to(Point::new(bounds.right(), bounds.bottom()))
            .line_to(Point::new(bounds.left(), bounds.bottom()))
            .close()
    }

    pub fn circle(center: Point, radius: f64) -> Self {
        Self::new()
            .move_to(Point::new(center.x + radius, center.y))
            .arc(center, radius, 0.0, 2.0 * std::f64::consts::PI, false)
            .close()
    }

    /// Create an ellipse path
    pub fn ellipse(center: Point, width: f64, height: f64) -> Self {
        let rx = width / 2.0;
        let ry = height / 2.0;

        // Approximate ellipse with 4 cubic bezier curves
        let kappa = 0.5522848; // Magic number for circle approximation
        let cx = center.x;
        let cy = center.y;

        Self::new()
            .move_to(Point::new(cx + rx, cy))
            .cubic_to(
                Point::new(cx + rx, cy + ry * kappa),
                Point::new(cx + rx * kappa, cy + ry),
                Point::new(cx, cy + ry),
            )
            .cubic_to(
                Point::new(cx - rx * kappa, cy + ry),
                Point::new(cx - rx, cy + ry * kappa),
                Point::new(cx - rx, cy),
            )
            .cubic_to(
                Point::new(cx - rx, cy - ry * kappa),
                Point::new(cx - rx * kappa, cy - ry),
                Point::new(cx, cy - ry),
            )
            .cubic_to(
                Point::new(cx + rx * kappa, cy - ry),
                Point::new(cx + rx, cy - ry * kappa),
                Point::new(cx + rx, cy),
            )
            .close()
    }

    /// Create a rounded rectangle path
    pub fn rounded_rect(bounds: Bounds, radius: f64) -> Self {
        let r = radius
            .min(bounds.size.width / 2.0)
            .min(bounds.size.height / 2.0);

        if r <= 0.0 {
            return Self::rect(bounds);
        }

        let left = bounds.left();
        let right = bounds.right();
        let top = bounds.top();
        let bottom = bounds.bottom();

        Self::new()
            .move_to(Point::new(left + r, top))
            .line_to(Point::new(right - r, top))
            .arc(
                Point::new(right - r, top + r),
                r,
                -std::f64::consts::PI / 2.0,
                0.0,
                false,
            )
            .line_to(Point::new(right, bottom - r))
            .arc(
                Point::new(right - r, bottom - r),
                r,
                0.0,
                std::f64::consts::PI / 2.0,
                false,
            )
            .line_to(Point::new(left + r, bottom))
            .arc(
                Point::new(left + r, bottom - r),
                r,
                std::f64::consts::PI / 2.0,
                std::f64::consts::PI,
                false,
            )
            .line_to(Point::new(left, top + r))
            .arc(
                Point::new(left + r, top + r),
                r,
                std::f64::consts::PI,
                -std::f64::consts::PI / 2.0,
                false,
            )
            .close()
    }
}

impl Default for Path {
    fn default() -> Self {
        Self::new()
    }
}
