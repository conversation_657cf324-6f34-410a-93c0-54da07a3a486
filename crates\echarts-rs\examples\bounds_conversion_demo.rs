//! EchartsBounds 与 gpui::Bounds 转换演示
//!
//! 这个示例展示了如何使用新的 From trait 实现在 EchartsBounds 和 gpui::Bounds<gpui::Pixels> 之间进行转换

use echarts_core::{Bounds as EchartsBounds, Point as EchartsPoint, Size as EchartsSize};
use gpui::{Bounds as GpuiBounds, Point as GpuiPoint, Size as GpuiSize, Pixels, px, point, size};

fn main() {
    println!("🔄 EchartsBounds 与 gpui::Bounds 转换演示");
    println!("=" .repeat(50));

    // 1. 测试 EchartsBounds -> gpui::Bounds 转换
    println!("\n📊 1. EchartsBounds -> gpui::Bounds 转换");
    let echarts_bounds = EchartsBounds::new(10.0, 20.0, 300.0, 200.0);
    println!("  原始 EchartsBounds: {:?}", echarts_bounds);
    
    let gpui_bounds: GpuiBounds<Pixels> = echarts_bounds.into();
    println!("  转换后 gpui::Bounds: {:?}", gpui_bounds);
    println!("  - origin.x: {}", gpui_bounds.origin.x.0);
    println!("  - origin.y: {}", gpui_bounds.origin.y.0);
    println!("  - size.width: {}", gpui_bounds.size.width.0);
    println!("  - size.height: {}", gpui_bounds.size.height.0);

    // 2. 测试 gpui::Bounds -> EchartsBounds 转换
    println!("\n📊 2. gpui::Bounds -> EchartsBounds 转换");
    let gpui_bounds = GpuiBounds::new(
        point(px(50.0), px(60.0)),
        size(px(400.0), px(300.0))
    );
    println!("  原始 gpui::Bounds: {:?}", gpui_bounds);
    
    let echarts_bounds: EchartsBounds = gpui_bounds.into();
    println!("  转换后 EchartsBounds: {:?}", echarts_bounds);
    println!("  - origin.x: {}", echarts_bounds.origin.x);
    println!("  - origin.y: {}", echarts_bounds.origin.y);
    println!("  - size.width: {}", echarts_bounds.size.width);
    println!("  - size.height: {}", echarts_bounds.size.height);

    // 3. 测试 Point 转换
    println!("\n📍 3. Point 转换测试");
    let echarts_point = EchartsPoint::new(100.0, 150.0);
    println!("  原始 EchartsPoint: {:?}", echarts_point);
    
    let gpui_point: GpuiPoint<Pixels> = echarts_point.into();
    println!("  转换后 gpui::Point: {:?}", gpui_point);
    
    let echarts_point_back: EchartsPoint = gpui_point.into();
    println!("  转换回 EchartsPoint: {:?}", echarts_point_back);

    // 4. 测试 Size 转换
    println!("\n📏 4. Size 转换测试");
    let echarts_size = EchartsSize::new(800.0, 600.0);
    println!("  原始 EchartsSize: {:?}", echarts_size);
    
    let gpui_size: GpuiSize<Pixels> = echarts_size.into();
    println!("  转换后 gpui::Size: {:?}", gpui_size);
    
    let echarts_size_back: EchartsSize = gpui_size.into();
    println!("  转换回 EchartsSize: {:?}", echarts_size_back);

    // 5. 测试往返转换的精度
    println!("\n🔄 5. 往返转换精度测试");
    let original = EchartsBounds::new(123.456, 789.012, 345.678, 901.234);
    println!("  原始值: {:?}", original);
    
    let gpui_converted: GpuiBounds<Pixels> = original.into();
    let back_converted: EchartsBounds = gpui_converted.into();
    println!("  往返转换后: {:?}", back_converted);
    
    let precision_loss_x = (original.origin.x - back_converted.origin.x).abs();
    let precision_loss_y = (original.origin.y - back_converted.origin.y).abs();
    let precision_loss_w = (original.size.width - back_converted.size.width).abs();
    let precision_loss_h = (original.size.height - back_converted.size.height).abs();
    
    println!("  精度损失:");
    println!("    - X: {:.6}", precision_loss_x);
    println!("    - Y: {:.6}", precision_loss_y);
    println!("    - Width: {:.6}", precision_loss_w);
    println!("    - Height: {:.6}", precision_loss_h);
    
    let max_loss = precision_loss_x.max(precision_loss_y).max(precision_loss_w).max(precision_loss_h);
    if max_loss < 0.001 {
        println!("  ✅ 精度损失在可接受范围内 (< 0.001)");
    } else {
        println!("  ⚠️  精度损失较大: {:.6}", max_loss);
    }

    // 6. 实际使用场景演示
    println!("\n🎯 6. 实际使用场景演示");
    demonstrate_practical_usage();

    println!("\n🎉 转换演示完成！");
}

/// 演示实际使用场景
fn demonstrate_practical_usage() {
    println!("  模拟图表渲染场景:");
    
    // 模拟从 GPUI 获取的窗口边界
    let window_bounds = GpuiBounds::new(
        point(px(0.0), px(0.0)),
        size(px(1200.0), px(800.0))
    );
    println!("    窗口边界 (GPUI): {:?}", window_bounds);
    
    // 转换为 EchartsBounds 用于图表计算
    let chart_bounds: EchartsBounds = window_bounds.into();
    println!("    图表边界 (ECharts): {:?}", chart_bounds);
    
    // 计算图表内容区域（减去边距）
    let margin = 50.0;
    let content_bounds = EchartsBounds::new(
        chart_bounds.origin.x + margin,
        chart_bounds.origin.y + margin,
        chart_bounds.size.width - 2.0 * margin,
        chart_bounds.size.height - 2.0 * margin,
    );
    println!("    内容区域 (ECharts): {:?}", content_bounds);
    
    // 转换回 GPUI 格式用于渲染
    let render_bounds: GpuiBounds<Pixels> = content_bounds.into();
    println!("    渲染边界 (GPUI): {:?}", render_bounds);
    
    println!("    ✅ 转换流程完成，可以开始渲染图表");
}
