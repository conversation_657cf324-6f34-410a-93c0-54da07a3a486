//! 响应式边界更新演示
//!
//! 这个示例展示了如何使用新的响应式架构：
//! 1. 边界变化时自动通知外部
//! 2. 外部更新坐标系统和数据
//! 3. 动态更新绘制命令
//! 4. 实现真正的响应式图表

use echarts_rs::{LineSeries, Color, CartesianCoordinateSystem, Bounds as EchartsBounds, Series};
use echarts_core::DrawCommand;
use gpui_renderer::{EChartsCanvas, EChartsElement};
use gpui::Bounds as GpuiBounds;
use gpui::*;
use std::sync::{Arc, Mutex};
use std::cell::RefCell;
use std::rc::Rc;

fn main() {
    println!("🚀 启动响应式边界更新演示...");

    App::new().run(move |cx: &mut AppContext| {
        println!("📱 应用程序上下文已创建");
        
        let displays = cx.displays();
        let display = displays.first().expect("无法获取显示器信息");
        println!("🖥️  显示器信息: {:?}", display.bounds());

        let window_options = WindowOptions {
            window_bounds: Some(WindowBounds::Windowed(Bounds {
                origin: point(px(100.0), px(100.0)),
                size: size(px(900.0), px(700.0)),
            })),
            titlebar: Some(TitlebarOptions {
                title: Some("响应式边界更新演示".into()),
                appears_transparent: false,
                traffic_light_position: None,
            }),
            window_background: WindowBackgroundAppearance::Opaque,
            focus: true,
            show: true,
            kind: WindowKind::Normal,
            is_movable: true,
            fullscreen: false,
            window_min_size: Some(size(px(600.0), px(400.0))),
        };
        
        cx.open_window(window_options, |_window, cx| {
            println!("✅ 窗口已创建，正在初始化响应式演示...");
            cx.new(|cx| ResponsiveBoundsDemo::new(cx))
        }).expect("无法创建窗口");
    });
}

/// 响应式边界演示组件
struct ResponsiveBoundsDemo {
    /// 当前系列
    current_series: Box<dyn Series>,
    /// 当前坐标系统
    coord_system: CartesianCoordinateSystem,
    /// 缓存的绘制命令
    cached_commands: Option<Vec<DrawCommand>>,
    /// 当前边界
    current_bounds: Option<GpuiBounds<Pixels>>,
    /// 更新计数器
    update_count: usize,
    /// 共享状态（用于在回调中更新）
    shared_state: Rc<RefCell<SharedState>>,
}

/// 共享状态结构
#[derive(Debug)]
struct SharedState {
    last_bounds: Option<GpuiBounds<Pixels>>,
    update_count: usize,
    commands_count: usize,
}

impl ResponsiveBoundsDemo {
    fn new(_cx: &mut Context<Self>) -> Self {
        println!("🎯 初始化响应式边界演示组件");
        
        // 创建共享状态
        let shared_state = Rc::new(RefCell::new(SharedState {
            last_bounds: None,
            update_count: 0,
            commands_count: 0,
        }));
        
        // 创建线图系列
        let current_series = Self::create_dynamic_line_series();
        println!("📊 动态线图系列已创建");

        // 创建初始坐标系统
        let coord_system = CartesianCoordinateSystem::new(
            EchartsBounds::new(0.0, 0.0, 800.0, 600.0),
            (0.0, 20.0),  // x 轴数据范围
            (0.0, 120.0), // y 轴数据范围
        );
        
        println!("📐 初始坐标系统已创建");

        Self {
            current_series,
            coord_system,
            cached_commands: None,
            current_bounds: None,
            update_count: 0,
            shared_state,
        }
    }
    
    /// 创建动态线图系列
    fn create_dynamic_line_series() -> Box<dyn Series> {
        println!("📈 创建动态折线图系列...");

        // 创建更丰富的示例数据
        let data: Vec<(f64, f64)> = (0..20)
            .map(|i| {
                let x = i as f64;
                let y = 60.0 + 40.0 * (x * 0.3).sin() + 20.0 * (x * 0.8).cos() + 10.0 * (x * 1.5).sin();
                (x, y)
            })
            .collect();

        println!("📊 生成了 {} 个数据点", data.len());

        Box::new(
            LineSeries::new("动态响应式折线图")
                .data(data)
                .color(Color::rgb(0.2, 0.6, 0.9))
                .line_width(3.0)
                .smooth(true)
                .show_points(true)
                .point_size(6.0),
        )
    }
    
    /// 响应式更新处理
    fn handle_bounds_change(&mut self, bounds: GpuiBounds<Pixels>) {
        println!("\n🔄 处理边界变化...");
        
        // 检查边界是否真的变化了
        if let Some(ref current) = self.current_bounds {
            if Self::bounds_equal(current, &bounds) {
                println!("⏭️  边界未变化，跳过更新");
                return;
            }
        }
        
        // 更新计数器
        self.update_count += 1;
        self.current_bounds = Some(bounds);
        
        // 更新共享状态
        {
            let mut state = self.shared_state.borrow_mut();
            state.last_bounds = Some(bounds);
            state.update_count = self.update_count;
        }
        
        println!("📊 第 {} 次边界更新", self.update_count);
        
        // 转换边界格式
        let echarts_bounds = EchartsBounds::new(
            bounds.origin.x.0 as f64,
            bounds.origin.y.0 as f64,
            bounds.size.width.0 as f64,
            bounds.size.height.0 as f64,
        );
        
        // 动态调整数据范围（根据边界大小）
        let x_range = if bounds.size.width.0 > 600.0 { (0.0, 20.0) } else { (0.0, 15.0) };
        let y_range = if bounds.size.height.0 > 400.0 { (0.0, 120.0) } else { (0.0, 100.0) };
        
        // 更新坐标系统
        self.coord_system = CartesianCoordinateSystem::new(
            echarts_bounds,
            x_range,
            y_range,
        );
        
        // 重新生成绘制命令
        match self.current_series.render_to_commands(&self.coord_system) {
            Ok(new_commands) => {
                self.cached_commands = Some(new_commands.clone());
                
                // 更新共享状态
                {
                    let mut state = self.shared_state.borrow_mut();
                    state.commands_count = new_commands.len();
                }
                
                println!("✅ 响应式更新成功:");
                println!("   📏 新边界: {:.1}x{:.1} at ({:.1}, {:.1})", 
                    echarts_bounds.size.width, 
                    echarts_bounds.size.height,
                    echarts_bounds.origin.x, 
                    echarts_bounds.origin.y
                );
                println!("   🎯 数据范围: X({:.1}, {:.1}), Y({:.1}, {:.1})", 
                    x_range.0, x_range.1, y_range.0, y_range.1
                );
                println!("   🎨 绘制命令: {} 个", new_commands.len());
            }
            Err(e) => {
                println!("❌ 响应式更新失败: {:?}", e);
                self.cached_commands = None;
            }
        }
    }
    
    /// 比较两个边界是否相等
    fn bounds_equal(a: &GpuiBounds<Pixels>, b: &GpuiBounds<Pixels>) -> bool {
        a.origin.x == b.origin.x 
            && a.origin.y == b.origin.y
            && a.size.width == b.size.width
            && a.size.height == b.size.height
    }
}

impl Render for ResponsiveBoundsDemo {
    fn render(&mut self, _window: &mut Window, _cx: &mut Context<Self>) -> impl IntoElement {
        println!("🎨 渲染响应式边界演示界面");
        
        div()
            .flex()
            .flex_col()
            .size_full()
            .bg(rgb(0xf5f5f5))
            .child(
                // 标题栏
                div()
                    .flex()
                    .items_center()
                    .justify_center()
                    .h(px(60.0))
                    .bg(rgb(0x2563eb))
                    .text_color(rgb(0xffffff))
                    .child(
                        div()
                            .text_xl()
                            .font_semibold()
                            .child("🔄 响应式边界更新演示")
                    )
            )
            .child(
                // 信息面板
                div()
                    .flex()
                    .justify_between()
                    .p_4()
                    .bg(rgb(0xe5e7eb))
                    .child(
                        div()
                            .text_sm()
                            .child(format!("更新次数: {}", self.update_count))
                    )
                    .child(
                        div()
                            .text_sm()
                            .child(
                                if let Some(ref bounds) = self.current_bounds {
                                    format!("当前尺寸: {:.0}x{:.0}", 
                                        bounds.size.width.0, 
                                        bounds.size.height.0
                                    )
                                } else {
                                    "等待边界信息...".to_string()
                                }
                            )
                    )
                    .child(
                        div()
                            .text_sm()
                            .child(
                                if let Some(ref commands) = self.cached_commands {
                                    format!("绘制命令: {} 个", commands.len())
                                } else {
                                    "未生成命令".to_string()
                                }
                            )
                    )
            )
            .child(
                // 图表区域
                div()
                    .flex_1()
                    .p_4()
                    .child(
                        div()
                            .size_full()
                            .bg(rgb(0xffffff))
                            .border_1()
                            .border_color(rgb(0xd1d5db))
                            .rounded_lg()
                            .shadow_lg()
                            .p_2()
                            .child({
                                // 创建响应式图表元素
                                let canvas = EChartsCanvas::new(
                                    self.current_series.clone_series(),
                                    self.coord_system.clone(),
                                ).with_debug(true);
                                
                                // 创建带有完整响应式回调的元素
                                let shared_state = self.shared_state.clone();
                                canvas.into_element()
                                    .with_bounds_callback(move |bounds| {
                                        println!("\n📐 边界变化回调触发: {:?}", bounds);
                                        
                                        // 更新共享状态
                                        {
                                            let mut state = shared_state.borrow_mut();
                                            state.last_bounds = Some(bounds);
                                            state.update_count += 1;
                                        }
                                        
                                        // 输出详细的边界信息
                                        println!("🔍 边界详情:");
                                        println!("   位置: ({:.1}, {:.1})", bounds.origin.x.0, bounds.origin.y.0);
                                        println!("   尺寸: {:.1} x {:.1}", bounds.size.width.0, bounds.size.height.0);
                                        println!("   面积: {:.1} 平方像素", 
                                            bounds.size.width.0 * bounds.size.height.0
                                        );
                                        
                                        // 在实际应用中，这里可以发送消息到主线程
                                        // 或者使用其他状态管理方案来更新UI
                                        println!("💡 提示: 在实际应用中，这里可以触发状态更新");
                                    })
                            })
                    )
            )
            .child(
                // 底部说明
                div()
                    .p_4()
                    .bg(rgb(0xf3f4f6))
                    .text_sm()
                    .text_color(rgb(0x6b7280))
                    .child("💡 调整窗口大小以查看响应式边界更新效果。图表会自动适应新的尺寸并重新生成绘制命令。")
            )
    }
}
