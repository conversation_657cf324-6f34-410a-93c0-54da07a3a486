//! 响应式边界更新演示
//!
//! 这个示例展示了如何使用新的响应式架构：
//! 1. 边界变化时自动通知外部
//! 2. 外部更新坐标系统和数据
//! 3. 动态更新绘制命令
//! 4. 实现真正的响应式图表

use echarts_rs::{LineSeries, Color, CartesianCoordinateSystem, Bounds as EchartsBounds, Series};
use echarts_core::DrawCommand;
use gpui_renderer::{EChartsCanvas, EChartsElement, ResponsiveChartState, GpuiCommandUpdater};
use gpui::{Bounds as GpuiBounds, BackgroundExecutor, Timer};
use std::sync::{Arc, RwLock};
use tokio::time::Duration;

fn main() {
    println!("🚀 启动多线程响应式边界更新演示...");
    println!("💡 这个演示展示了如何使用多线程安全的共享状态来处理边界变化");

    // 简化的演示 - 直接创建和测试多线程组件
    test_multithreaded_responsive_state();
}

/// 测试 GPUI 后台执行器响应式状态
fn test_multithreaded_responsive_state() {
    println!("\n🧵 测试 GPUI 后台执行器响应式状态...");

    // 创建 GPUI 后台执行器（模拟）
    // 注意：在实际应用中，这应该从 GPUI 应用上下文中获取
    let bg_executor = Arc::new(BackgroundExecutor::new(std::thread::available_parallelism().unwrap_or(std::num::NonZeroUsize::new(4).unwrap())));

    // 创建初始坐标系统
    let coord_system = CartesianCoordinateSystem::new(
        EchartsBounds::new(0.0, 0.0, 800.0, 600.0),
        (0.0, 20.0),
        (0.0, 120.0),
    );

    // 创建响应式图表状态
    let chart_state = ResponsiveChartState::new(coord_system, bg_executor);

    // 创建 GPUI 命令更新器
    let command_updater = chart_state.create_command_updater();

    // 创建测试数据
    let data: Vec<(f64, f64)> = (0..10)
        .map(|i| {
            let x = i as f64;
            let y = 50.0 + 30.0 * (x * 0.5).sin();
            (x, y)
        })
        .collect();

    let series = LineSeries::new("测试线图")
        .data(data)
        .color(Color::rgb(0.2, 0.6, 0.9))
        .line_width(2.0)
        .smooth(true);

    println!("📊 创建了测试系列，包含 {} 个数据点", series.data.len());

    // 测试多线程更新
    test_concurrent_updates(chart_state, command_updater, Box::new(series));
}

/// 测试并发更新
fn test_concurrent_updates(
    chart_state: ResponsiveChartState,
    command_updater: ThreadedCommandUpdater,
    series: Box<dyn Series>,
) {
    println!("\n🔄 开始并发更新测试...");

    // 模拟多个边界变化
    let test_bounds = vec![
        (800.0, 600.0),
        (1024.0, 768.0),
        (1200.0, 900.0),
        (640.0, 480.0),
        (1920.0, 1080.0),
    ];

    let mut handles = vec![];

    for (i, (width, height)) in test_bounds.into_iter().enumerate() {
        let chart_state_clone = chart_state.clone();
        let command_updater_clone = command_updater.clone();
        let series_clone = series.clone_series();

        let handle = thread::spawn(move || {
            println!("🧵 线程 {} 开始处理边界 {}x{}", i, width, height);

            // 创建新的坐标系统
            let echarts_bounds = EchartsBounds::new(0.0, 0.0, width, height);
            let x_range = if width > 800.0 { (0.0, 20.0) } else { (0.0, 15.0) };
            let y_range = if height > 600.0 { (0.0, 120.0) } else { (0.0, 100.0) };

            let new_coord_system = CartesianCoordinateSystem::new(
                echarts_bounds,
                x_range,
                y_range,
            );

            // 更新坐标系统
            if let Err(e) = chart_state_clone.update_coord_system(new_coord_system.clone()) {
                println!("❌ 线程 {} 更新坐标系统失败: {:?}", i, e);
                return;
            }

            // 生成绘制命令
            match series_clone.render_to_commands(&new_coord_system) {
                Ok(commands) => {
                    // 同步更新命令
                    if let Err(e) = command_updater_clone.update_sync(commands.clone()) {
                        println!("❌ 线程 {} 更新命令失败: {:?}", i, e);
                    } else {
                        println!("✅ 线程 {} 成功更新 {} 个绘制命令", i, commands.len());
                    }
                }
                Err(e) => {
                    println!("❌ 线程 {} 生成绘制命令失败: {:?}", i, e);
                }
            }

            // 模拟一些处理时间
            thread::sleep(Duration::from_millis(100));

            println!("🏁 线程 {} 完成处理", i);
        });

        handles.push(handle);
    }

    // 等待所有线程完成
    for (i, handle) in handles.into_iter().enumerate() {
        if let Err(e) = handle.join() {
            println!("❌ 线程 {} 执行失败: {:?}", i, e);
        }
    }

    // 输出最终状态
    println!("\n📊 最终状态:");
    println!("   更新计数: {}", chart_state.get_update_count());

    if let Some(commands) = chart_state.get_commands() {
        println!("   绘制命令: {} 个", commands.len());
    } else {
        println!("   绘制命令: 无");
    }

    if let Some(coord_system) = chart_state.get_coord_system() {
        println!("   坐标系统边界: {:?}", coord_system.bounds);
    } else {
        println!("   坐标系统: 无");
    }

    println!("\n🎉 多线程响应式边界更新演示完成！");
    println!("💡 这展示了如何使用 Arc<RwLock<T>> 实现线程安全的状态共享");
}

// 删除了 UI 组件，这个演示专注于多线程功能测试
