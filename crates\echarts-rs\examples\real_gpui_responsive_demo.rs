//! 真实 GPUI 响应式边界更新演示
//! 
//! 本演示展示了如何在真实的 GPUI 应用中实现响应式图表：
//! 1. 使用 GPUI 的 BackgroundExecutor 进行异步处理
//! 2. 监听窗口大小变化事件
//! 3. 自动更新坐标系统和绘制命令
//! 4. 实现真正的响应式图表界面

use echarts_rs::{LineSeries, Color, CartesianCoordinateSystem, Bounds as EchartsBounds, Series};
use gpui_renderer::{EChartsCanvas, EChartsElement, ResponsiveChartState, GpuiCommandUpdater};
use gpui::*;
use std::sync::Arc;


fn generate_test_data(n: usize) -> Vec<Pixels> {
    (0..n)
        .map(|i| {
            let x = i as f32 * 0.1;
            px(x.sin())
        })
        .collect()
}

fn main() {
    println!("🚀 启动真实 GPUI 响应式边界更新演示...");

    Application::new().run(move |cx| {
        println!("📱 GPUI 应用程序上下文已创建");
        
        println!("📱 GPUI 应用程序上下文已创建");

        // 计算窗口大小
        let window_size = size(px(1000.0), px(800.0));

        // 创建窗口
        let _window = cx.open_window(
            WindowOptions {
                window_bounds: Some(WindowBounds::Windowed(Bounds::centered(
                    None,
                    window_size,
                    cx,
                ))),
                titlebar: Some(TitlebarOptions {
                    title: Some("🔄 真实 GPUI 响应式图表演示".into()),
                    appears_transparent: false,
                    traffic_light_position: None,
                }),
                focus: true,
                show: true,
                kind: WindowKind::Normal,
                is_movable: true,
                display_id: None,
                window_background: WindowBackgroundAppearance::Opaque,
                app_id: None,
                window_min_size: Some(size(px(600.0), px(400.0))),
                window_decorations: None,
            },
            |_window, cx| {
                println!("✅ 窗口已创建，正在初始化真实响应式演示...");
                cx.new(|cx| RealGpuiResponsiveDemo::new(cx))
            },
        );
    });
}

/// 真实 GPUI 响应式演示组件
struct RealGpuiResponsiveDemo {
    /// 当前系列
    current_series: Box<dyn Series>,
    /// 响应式图表状态（使用真实的 GPUI BackgroundExecutor）
    chart_state: ResponsiveChartState,
    /// GPUI 命令更新器
    command_updater: GpuiCommandUpdater,
    /// 更新计数器（本地）
    local_update_count: usize,
    /// 当前窗口尺寸
    current_window_size: Option<Size<Pixels>>,
}

impl RealGpuiResponsiveDemo {
    fn new(cx: &mut Context<Self>) -> Self {
        println!("🎯 初始化真实 GPUI 响应式边界演示组件");

        // 创建线图系列
        let current_series = Self::create_dynamic_line_series();
        println!("📊 动态线图系列已创建");

        // 获取 GPUI 的 BackgroundExecutor
        let bg_executor = Arc::new(cx.background_executor().clone());
        println!("🧵 获取到真实的 GPUI BackgroundExecutor");

        // 创建初始坐标系统
        let coord_system = CartesianCoordinateSystem::new(
            EchartsBounds::new(0.0, 0.0, 1000.0, 800.0),
            (0.0, 20000.0),  // x 轴数据范围
            (0.0, 12000.0), // y 轴数据范围
        );

        // 创建响应式图表状态（使用真实的 GPUI BackgroundExecutor）
        let chart_state = ResponsiveChartState::new(coord_system, bg_executor);

        // 创建 GPUI 命令更新器
        let command_updater = chart_state.create_command_updater();

        println!("📐 真实 GPUI 响应式状态已创建");

        Self {
            current_series,
            chart_state,
            command_updater,
            local_update_count: 0,
            current_window_size: None,
        }
    }
    
    /// 创建动态线图系列
    fn create_dynamic_line_series() -> Box<dyn Series> {
        println!("📈 创建动态折线图系列...");

        // 创建更丰富的示例数据
        let data: Vec<(f64, f64)> = (0..20000)
            .map(|i| {
                let x = i as f64;
                let y = 60.0 + 40.0 * (x * 0.3).sin() + 20.0 * (x * 0.8).cos() + 10.0 * (x * 1.5).sin();
                (x, y)
            })
            .collect();
        // let data: Vec<(f64, f64)> = (0..20000)
        //     .map(|i| {
        //         let x = i as f64;
        //         let y = 60.0 + 40.0 * (x * 0.3).sin() + 20.0 * (x * 0.8).cos() + 10.0 * (x * 1.5).sin();
        //         (x, y)
        //     })
        //     .collect();

        println!("📊 生成了 {} 个数据点", data.len());

        Box::new(
            LineSeries::new("真实 GPUI 响应式折线图")
                .data(data)
                .color(Color::rgb(0.2, 0.6, 0.9))
                .line_width(3.0)
                .smooth(true),
        )
    }
    
    /// 真实 GPUI 响应式更新处理
    fn handle_bounds_change_gpui(&mut self, bounds: Bounds<Pixels>, cx: &mut Context<Self>) {
        println!("\n🔄 真实 GPUI 处理边界变化...");
        println!("📏 新边界: {:.1}x{:.1} at ({:.1}, {:.1})",
            bounds.size.width.0,
            bounds.size.height.0,
            bounds.origin.x.0,
            bounds.origin.y.0
        );

        // 检查边界是否真的变化了
        let size_changed = self.current_window_size
            .map(|current_size| current_size != bounds.size)
            .unwrap_or(true);

        if !size_changed {
            println!("⏭️  窗口尺寸未变化，跳过更新");
            return;
        }

        // 更新本地状态
        self.current_window_size = Some(bounds.size);
        self.local_update_count += 1;

        println!("📊 第 {} 次真实 GPUI 边界更新", self.local_update_count);

        // 转换边界格式
        let echarts_bounds = EchartsBounds::new(
            bounds.origin.x.0 as f64,
            bounds.origin.y.0 as f64,
            bounds.size.width.0 as f64,
            bounds.size.height.0 as f64,
        );

        // 动态调整数据范围（根据边界大小）
        let x_range = if bounds.size.width.0 > 800.0 { (0.0, 20.0) } else { (0.0, 15.0) };
        let y_range = if bounds.size.height.0 > 600.0 { (0.0, 120.0) } else { (0.0, 100.0) };

        // 创建新的坐标系统
        let new_coord_system = CartesianCoordinateSystem::new(
            echarts_bounds,
            x_range,
            y_range,
        );

        // 更新共享坐标系统
        if let Err(e) = self.chart_state.update_coord_system(new_coord_system.clone()) {
            println!("❌ 更新坐标系统失败: {:?}", e);
            return;
        }

        // 在 GPUI 后台执行器中重新生成绘制命令
        let series = self.current_series.clone_series();
        let command_updater = self.command_updater.clone();
        let update_count = self.local_update_count;

        // 使用真实的 GPUI BackgroundExecutor
        cx.background_executor()
            .spawn(async move {
                println!("🧵 GPUI 后台执行器开始生成绘制命令 (更新 {})", update_count);

                match series.render_to_commands(&new_coord_system) {
                    Ok(new_commands) => {
                        // 异步更新命令
                        command_updater.update_async(new_commands.clone());

                        println!("✅ GPUI 后台执行器更新成功 (更新 {}):", update_count);
                        println!("   📏 新边界: {:.1}x{:.1} at ({:.1}, {:.1})",
                            echarts_bounds.size.width,
                            echarts_bounds.size.height,
                            echarts_bounds.origin.x,
                            echarts_bounds.origin.y
                        );
                        println!("   🎯 数据范围: X({:.1}, {:.1}), Y({:.1}, {:.1})",
                            x_range.0, x_range.1, y_range.0, y_range.1
                        );
                        println!("   🎨 绘制命令: {} 个", new_commands.len());
                    }
                    Err(e) => {
                        println!("❌ GPUI 后台执行器更新失败 (更新 {}): {:?}", update_count, e);
                    }
                }
            })
            .detach();
    }
}

impl Render for RealGpuiResponsiveDemo {
    fn render(&mut self, _window: &mut Window, _cx: &mut Context<Self>) -> impl IntoElement {
        println!("🎨 渲染真实 GPUI 响应式边界演示界面");

        div()
            .flex()
            .flex_col()
            .size_full()
            .bg(rgb(0xf5f5f5))
            .child(
                // 标题栏
                div()
                    .flex()
                    .items_center()
                    .justify_center()
                    .h(px(60.0))
                    .bg(rgb(0x2563eb))
                    .text_color(rgb(0xffffff))
                    .child(
                        div()
                            .text_xl()
                            .font_weight(FontWeight::SEMIBOLD)
                            .child("🔄 真实 GPUI 响应式边界更新演示")
                    )
            )
            .child(
                // 信息面板
                div()
                    .flex()
                    .justify_between()
                    .p_4()
                    .bg(rgb(0xe5e7eb))
                    .child(
                        div()
                            .text_sm()
                            .child(format!("本地更新: {} | 共享更新: {}",
                                self.local_update_count,
                                self.chart_state.get_update_count()
                            ))
                    )
                    .child(
                        div()
                            .text_sm()
                            .child(
                                if let Some(size) = self.current_window_size {
                                    format!("当前尺寸: {:.0}x{:.0}",
                                        size.width.0,
                                        size.height.0
                                    )
                                } else {
                                    "等待窗口尺寸...".to_string()
                                }
                            )
                    )
                    .child(
                        div()
                            .text_sm()
                            .child(
                                if let Some(commands) = self.chart_state.get_commands() {
                                    format!("共享命令: {} 个", commands.len())
                                } else {
                                    "未生成命令".to_string()
                                }
                            )
                    )
                    .child(
                        div()
                            .text_sm()
                            .child("🧵 真实 GPUI BackgroundExecutor")
                    )
            )
            .child(
                // 图表区域
                div()
                    .flex_1()
                    .p_4()
                    .child(
                        div()
                            .size_full()
                            .bg(rgb(0xffffff))
                            .border_1()
                            .border_color(rgb(0xd1d5db))
                            .rounded_lg()
                            .shadow_lg()
                            .p_2()
                            .child({
                                // 获取当前坐标系统
                                let coord_system = self.chart_state.get_coord_system()
                                    .unwrap_or_else(|| CartesianCoordinateSystem::new(
                                        EchartsBounds::new(0.0, 0.0, 1000.0, 800.0),
                                        (0.0, 20.0),
                                        (0.0, 120.0),
                                    ));

                                // 创建真实 GPUI 响应式图表元素
                                let canvas = EChartsCanvas::new(
                                    self.current_series.clone_series(),
                                    coord_system,
                                ).with_debug(false);

                                // 使用响应式状态创建元素
                                EChartsElement::with_responsive_state(canvas, self.chart_state.clone())
                                    .with_bounds_callback({
                                        let chart_state = self.chart_state.clone();
                                        let command_updater = self.command_updater.clone();
                                        let series = self.current_series.clone_series();

                                        move |bounds| {
                                            println!("\n📐 真实 GPUI 边界变化回调触发: {:?}", bounds);

                                            // 更新边界状态
                                            if let Err(e) = chart_state.update_bounds(bounds) {
                                                println!("❌ 更新边界状态失败: {:?}", e);
                                                return;
                                            }

                                            // 输出详细的边界信息
                                            println!("🔍 真实 GPUI 边界详情:");
                                            println!("   位置: ({:.1}, {:.1})", bounds.origin.x.0, bounds.origin.y.0);
                                            println!("   尺寸: {:.1} x {:.1}", bounds.size.width.0, bounds.size.height.0);
                                            println!("   面积: {:.1} 平方像素",
                                                bounds.size.width.0 * bounds.size.height.0
                                            );

                                            // 在真实的 GPUI 后台执行器中异步更新绘制命令
                                            let chart_state_clone = chart_state.clone();
                                            let command_updater_clone = command_updater.clone();
                                            let series_clone = series.clone_series();

                                            // 使用 chart_state 中的 BackgroundExecutor
                                            chart_state.bg_executor.spawn(async move {
                                                // 创建新的坐标系统
                                                let echarts_bounds = EchartsBounds::new(
                                                    bounds.origin.x.0 as f64,
                                                    bounds.origin.y.0 as f64,
                                                    bounds.size.width.0 as f64,
                                                    bounds.size.height.0 as f64,
                                                );

                                                let x_range = if bounds.size.width.0 > 800.0 { (0.0, 20.0) } else { (0.0, 15.0) };
                                                let y_range = if bounds.size.height.0 > 600.0 { (0.0, 120.0) } else { (0.0, 100.0) };

                                                let new_coord_system = CartesianCoordinateSystem::new(
                                                    echarts_bounds,
                                                    x_range,
                                                    y_range,
                                                );

                                                // 更新坐标系统
                                                if let Err(e) = chart_state_clone.update_coord_system(new_coord_system.clone()) {
                                                    println!("❌ 真实 GPUI 后台执行器更新坐标系统失败: {:?}", e);
                                                    return;
                                                }

                                                // 生成新的绘制命令
                                                match series_clone.render_to_commands(&new_coord_system) {
                                                    Ok(commands) => {
                                                        // 异步更新命令
                                                        command_updater_clone.update_async(commands.clone());
                                                        println!("🧵 真实 GPUI 后台执行器成功更新 {} 个绘制命令", commands.len());
                                                    }
                                                    Err(e) => {
                                                        println!("❌ 真实 GPUI 后台执行器生成绘制命令失败: {:?}", e);
                                                    }
                                                }
                                            }).detach();

                                            println!("💡 真实 GPUI 响应式更新已启动");
                                        }
                                    })
                            })
                    )
            )
            .child(
                // 底部说明
                div()
                    .p_4()
                    .bg(rgb(0xf3f4f6))
                    .text_sm()
                    .text_color(rgb(0x6b7280))
                    .child("🧵 真实 GPUI 响应式演示：调整窗口大小查看效果。图表使用真实的 GPUI BackgroundExecutor 异步更新。")
            )
    }
}
