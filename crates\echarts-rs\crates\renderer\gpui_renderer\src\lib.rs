//! GPUI 渲染器 - 简洁实用版本
//!
//! 这是一个简化的 GPUI 渲染器实现，专注于核心功能和易用性。
//!
//! ## 特性
//! - 直接渲染 DrawCommand 到 GPUI 窗口
//! - 支持基础图形绘制（线条、矩形、圆形、文本、路径）
//! - 简单的性能优化
//! - 清晰的错误处理
//!
//! ## 使用示例
//! ```rust,no_run
//! use gpui_renderer::GpuiRenderer;
//! use echarts_core::{DrawCommand, Bounds};
//!
//! // 在实际应用中，这些参数会由图表系统提供
//! let mut renderer = GpuiRenderer::new();
//! // let draw_commands = vec![]; // 绘制命令
//! // let bounds = Bounds::new(0.0, 0.0, 800.0, 600.0); // 图表边界
//! // renderer.render_chart(draw_commands, bounds, cx, app)?;
//! ```

use echarts_core::DrawCommand;
use echarts_core::{Bounds, Point, Size, Result, LineStyle, CircleStyle, TextStyle, RectStyle, PathStyle, PolygonStyle, PointStyle, PathCommand, Color};
use echarts_themes::Theme;
use gpui::{
    Bounds as GpuiBounds, Pixels, Window, App, px,
    PathBuilder, Point as GpuiPoint, Size as GpuiSize, PaintQuad, Background,
    Hsla, SharedString, FontWeight, TextAlign as GpuiTextAlign, TextRun,
    point, size, Corners, IntoElement, Element, Styled, div
};

/// GPUI 渲染器
pub struct GpuiRenderer {
    /// 当前渲染边界
    bounds: Bounds,
    /// 主题
    theme: Theme,
    /// 渲染统计
    stats: RenderStats,
    /// 调试模式
    debug: bool,
}

/// 渲染统计信息
#[derive(Debug, Clone, Default)]
pub struct RenderStats {
    /// 渲染的命令数量
    pub commands_rendered: usize,
    /// 渲染时间（毫秒）
    pub render_time_ms: f64,
    /// 跳过的无效命令数量
    pub invalid_commands: usize,
}

impl GpuiRenderer {
    /// 创建新的渲染器
    pub fn new() -> Self {
        Self {
            bounds: Bounds::new(0.0, 0.0, 800.0, 600.0),
            theme: Theme::default(),
            stats: RenderStats::default(),
            debug: false,
        }
    }

    /// 启用调试模式
    pub fn with_debug(mut self, debug: bool) -> Self {
        self.debug = debug;
        self
    }

    /// 设置主题
    pub fn set_theme(&mut self, theme: Theme) {
        self.theme = theme;
    }

    /// 获取渲染统计
    pub fn stats(&self) -> &RenderStats {
        &self.stats
    }

    /// 重置统计信息
    pub fn reset_stats(&mut self) {
        self.stats = RenderStats::default();
    }

    /// 主渲染函数
    pub fn render_chart(
        &mut self,
        commands: Vec<DrawCommand>,
        bounds: gpui::Bounds<gpui::Pixels>,
        window: &mut Window,
        cx: &mut App,
    ) -> Result<()> {
        let start_time = std::time::Instant::now();

        // 更新渲染边界
        self.bounds = self.convert_bounds(bounds);

        if self.debug {
            println!("🎯 开始渲染 {} 个 DrawCommand", commands.len());
        }

        // 重置统计
        self.stats.commands_rendered = 0;
        self.stats.invalid_commands = 0;

        // 渲染每个命令
        for command in commands {
            if self.render_command(command, window, cx).is_ok() {
                self.stats.commands_rendered += 1;
            } else {
                self.stats.invalid_commands += 1;
            }
        }

        // 更新统计信息
        let elapsed = start_time.elapsed();
        self.stats.render_time_ms = elapsed.as_secs_f64() * 1000.0;

        if self.debug {
            println!("✅ 渲染完成，耗时 {:.2}ms", self.stats.render_time_ms);
            println!(
                "📊 成功: {}, 失败: {}",
                self.stats.commands_rendered, self.stats.invalid_commands
            );
        }

        Ok(())
    }

    /// 渲染单个命令
    fn render_command(&self, command: DrawCommand, window: &mut Window, cx: &mut App) -> Result<()> {
        match command {
            DrawCommand::Line { from, to, style } => {
                self.draw_line(from, to, &style, window, cx);
            }
            DrawCommand::Rect { bounds, style } => {
                self.draw_rect(bounds, &style, window, cx);
            }
            DrawCommand::Circle {
                center,
                radius,
                style,
            } => {
                self.draw_circle(center, radius, &style, window, cx);
            }
            DrawCommand::Text {
                text,
                position,
                style,
            } => {
                self.draw_text(&text, position, &style, window, cx);
            }
            DrawCommand::Path { commands, style } => {
                self.draw_path(&commands, &style, window, cx);
            }
            DrawCommand::Polygon { points, style } => {
                self.draw_polygon(&points, &style, window, cx);
            }
            DrawCommand::Points { points, style } => {
                self.draw_points(&points, &style, window, cx);
            }
            DrawCommand::Lines { segments, style } => {
                self.draw_lines(&segments, &style, window, cx);
            }
        }
        Ok(())
    }

    /// 绘制线条
    fn draw_line(&self, from: Point, to: Point, style: &LineStyle, window: &mut Window, _cx: &mut App) {
        if self.debug {
            println!(
                "绘制线条: {:?} -> {:?}, 颜色: {:?}, 宽度: {}",
                from, to, style.color, style.width
            );
        }

        // 创建路径构建器，设置线条宽度
        let mut builder = PathBuilder::stroke(px(style.width as f32));

        // 构建线条路径
        let start_point = GpuiPoint::new(px(from.x as f32), px(from.y as f32));
        let end_point = GpuiPoint::new(px(to.x as f32), px(to.y as f32));

        builder.move_to(start_point);
        builder.line_to(end_point);

        // 构建路径并绘制
        if let Ok(path) = builder.build() {
            let line_color = self.convert_color_to_hsla(style.color);
            window.paint_path(path, line_color);
        }
    }

    /// 绘制矩形
    fn draw_rect(&self, bounds: Bounds, style: &RectStyle, window: &mut Window, _cx: &mut App) {
        if self.debug {
            println!("绘制矩形: {:?}", bounds);
        }

        // 转换边界到 GPUI 格式
        let gpui_bounds = GpuiBounds::new(
            point(px(bounds.origin.x as f32), px(bounds.origin.y as f32)),
            size(px(bounds.size.width as f32), px(bounds.size.height as f32))
        );

        // 转换填充样式到 GPUI 背景
        let background = if let Some(fill_color) = style.fill {
            let mut hsla_color = self.convert_color_to_hsla(fill_color);
            hsla_color.a = style.opacity as f32;
            Background::from(hsla_color)
        } else {
            // 默认透明背景
            Background::from(Hsla { h: 0.0, s: 0.0, l: 0.0, a: 0.0 })
        };

        // 使用 GPUI 的 PaintQuad 绘制矩形
        let paint_quad = PaintQuad {
            bounds: gpui_bounds,
            background,
            border_widths: Default::default(),
            border_color: Default::default(),
            border_style: Default::default(),
            corner_radii: Default::default(),
        };
        window.paint_quad(paint_quad);
    }

    /// 绘制圆形
    fn draw_circle(&self, center: Point, radius: f64, style: &CircleStyle, window: &mut Window, _cx: &mut App) {
        if self.debug {
            println!("绘制圆形: 中心 {:?}, 半径 {}", center, radius);
        }

        // 创建圆形的边界框
        let gpui_bounds = GpuiBounds::new(
            point(px((center.x - radius) as f32), px((center.y - radius) as f32)),
            size(px((radius * 2.0) as f32), px((radius * 2.0) as f32))
        );

        // 转换填充样式到 GPUI 背景
        let background = if let Some(fill_color) = style.fill {
            let mut hsla_color = self.convert_color_to_hsla(fill_color);
            hsla_color.a = style.opacity as f32;
            Background::from(hsla_color)
        } else {
            // 默认透明背景
            Background::from(Hsla { h: 0.0, s: 0.0, l: 0.0, a: 0.0 })
        };

        // 设置圆角半径使其成为圆形
        let corner_radius = px(radius as f32);
        let corner_radii = Corners {
            top_left: corner_radius,
            top_right: corner_radius,
            bottom_left: corner_radius,
            bottom_right: corner_radius,
        };

        // 绘制圆角矩形（圆形）
        let paint_quad = PaintQuad {
            bounds: gpui_bounds,
            background,
            border_widths: Default::default(),
            border_color: Default::default(),
            border_style: Default::default(),
            corner_radii,
        };
        window.paint_quad(paint_quad);
    }

    /// 绘制文本
    ///
    /// 这是一个完整的文本绘制实现，支持：
    /// - 多种字体权重（从 Thin 到 Black）
    /// - 文本对齐（左、中、右）
    /// - 基线对齐（顶部、中间、底部）
    /// - 透明度控制
    /// - 错误处理和调试输出
    ///
    /// # 参数
    /// - `text`: 要绘制的文本内容
    /// - `position`: 文本位置
    /// - `style`: 文本样式配置
    /// - `window`: GPUI 窗口引用
    /// - `cx`: GPUI 应用上下文
    fn draw_text(&self, text: &str, position: Point, style: &TextStyle, window: &mut Window, cx: &mut App) {
        if self.debug {
            println!("绘制文本: '{}' at {:?}", text, position);
        }

        // 空文本检查
        if text.is_empty() {
            return;
        }

        // 转换 ECharts TextStyle 到 GPUI 类型
        let font_size = px(style.font_size.max(1.0) as f32); // 确保字体大小至少为1
        let mut text_color = self.convert_color_to_hsla(style.color);

        // 应用透明度
        text_color.a *= style.opacity as f32;

        let shared_text: SharedString = text.to_string().into();

        // 转换字体权重
        let font_weight = match style.font_weight {
            echarts_core::FontWeight::Thin => FontWeight::THIN,
            echarts_core::FontWeight::ExtraLight => FontWeight::LIGHT, // 映射到 LIGHT
            echarts_core::FontWeight::Light => FontWeight::LIGHT,
            echarts_core::FontWeight::Normal => FontWeight::NORMAL,
            echarts_core::FontWeight::Medium => FontWeight::MEDIUM,
            echarts_core::FontWeight::SemiBold => FontWeight::BOLD, // 映射到 BOLD
            echarts_core::FontWeight::Bold => FontWeight::BOLD,
            echarts_core::FontWeight::ExtraBold => FontWeight::EXTRA_BOLD,
            echarts_core::FontWeight::Black => FontWeight::BLACK,
        };

        // 转换文本对齐
        let text_align = match style.align {
            echarts_core::TextAlign::Left | echarts_core::TextAlign::Start => GpuiTextAlign::Left,
            echarts_core::TextAlign::Center => GpuiTextAlign::Center,
            echarts_core::TextAlign::Right | echarts_core::TextAlign::End => GpuiTextAlign::Right,
        };

        // 计算文本位置（考虑基线和旋转）
        let mut origin = point(px(position.x as f32), px(position.y as f32));

        // 根据基线调整Y位置
        match style.baseline {
            echarts_core::TextBaseline::Top => {
                // 文本顶部对齐到指定位置
                origin.y += font_size;
            }
            echarts_core::TextBaseline::Middle => {
                // 文本中心对齐到指定位置
                origin.y += font_size / 2.0;
            }
            echarts_core::TextBaseline::Bottom => {
                // 文本底部对齐到指定位置（默认）
                // 不需要调整
            }
            _ => {
                // 其他基线类型使用默认处理
            }
        }

        // 创建文本运行
        let text_run = TextRun {
            len: text.len(),
            font: window.text_style().highlight(font_weight).font(),
            color: text_color,
            background_color: None,
            underline: None,
            strikethrough: None,
        };

        // 使用 GPUI 的文本系统渲染文本
        match window.text_system().shape_text(shared_text, font_size, &[text_run], None, None) {
            Ok(shaped_text) => {
                for line in shaped_text {
                    // 根据对齐方式调整原点
                    let adjusted_origin = match text_align {
                        GpuiTextAlign::Left => origin,
                        GpuiTextAlign::Right => origin - point(line.size(font_size).width, px(0.0)),
                        GpuiTextAlign::Center => {
                            origin - point(line.size(font_size).width / 2.0, px(0.0))
                        }
                    };

                    // 绘制文本行
                    if let Err(e) = line.paint(
                        adjusted_origin,
                        font_size,
                        text_align,
                        None,
                        window,
                        cx,
                    ) {
                        if self.debug {
                            println!("文本绘制失败: {:?}", e);
                        }
                    }
                }
            }
            Err(e) => {
                if self.debug {
                    println!("文本塑形失败: {:?}", e);
                }
            }
        }

        if self.debug {
            println!(
                "✅ 文本绘制完成: '{}' at {:?}, 大小: {}, 颜色: {:?}, 对齐: {:?}",
                text, position, font_size.0, text_color, text_align
            );
        }
    }

    /// 绘制路径
    fn draw_path(&self, commands: &[PathCommand], style: &PathStyle, window: &mut Window, _cx: &mut App) {
        if self.debug {
            println!("绘制路径: {} 个命令", commands.len());
        }

        // 创建路径构建器
        let mut path_builder = if style.fill.is_some() {
            PathBuilder::fill()
        } else if let Some(stroke_style) = &style.stroke {
            PathBuilder::stroke(px(stroke_style.width as f32))
        } else {
            PathBuilder::stroke(px(1.0)) // 默认宽度
        };

        // 转换路径命令，跟踪当前位置以支持二次贝塞尔曲线转换
        let mut current_position = GpuiPoint::new(px(0.0), px(0.0));

        for command in commands {
            match command {
                PathCommand::MoveTo(point) => {
                    let gpui_point = GpuiPoint::new(px(point.x as f32), px(point.y as f32));
                    path_builder.move_to(gpui_point);
                    current_position = gpui_point;
                }
                PathCommand::LineTo(point) => {
                    let gpui_point = GpuiPoint::new(px(point.x as f32), px(point.y as f32));
                    path_builder.line_to(gpui_point);
                    current_position = gpui_point;
                }
                PathCommand::CurveTo { control1, control2, to } => {
                    let start_point = current_position;
                    let end_point = GpuiPoint::new(px(to.x as f32), px(to.y as f32));
                    let ctrl1_point = GpuiPoint::new(px(control1.x as f32), px(control1.y as f32));
                    let ctrl2_point = GpuiPoint::new(px(control2.x as f32), px(control2.y as f32));

                    // 使用完整的三次贝塞尔曲线
                    self.cubic_bezier_to(&mut path_builder, start_point, ctrl1_point, ctrl2_point, end_point);
                    current_position = end_point;

                    if self.debug {
                        println!(
                            "✅ 三次贝塞尔曲线: start={:?}, control1={:?}, control2={:?}, end={:?}",
                            start_point, ctrl1_point, ctrl2_point, end_point
                        );
                    }
                }
                PathCommand::QuadTo { control, to } => {
                    let start_point = current_position;
                    let control_point = GpuiPoint::new(px(control.x as f32), px(control.y as f32));
                    let end_point = GpuiPoint::new(px(to.x as f32), px(to.y as f32));

                    // 方法1：使用精确的数学转换（推荐）
                    let (control1, _control2) = self.quadratic_to_cubic_bezier(
                        start_point,
                        control_point,
                        end_point,
                    );

                    // 由于 GPUI 的 curve_to 只接受一个控制点，我们使用第一个控制点
                    // 这提供了比简单线性插值更好的近似效果
                    path_builder.curve_to(end_point, control1);
                    current_position = end_point;

                    // 方法2：如果需要更高精度，可以使用分段线性近似（备选方案）
                    // self.draw_quadratic_bezier_approximation(
                    //     &mut path_builder,
                    //     start_point,
                    //     control_point,
                    //     end_point,
                    //     8, // 8个线段通常足够平滑
                    // );

                    if self.debug {
                        println!(
                            "✅ 二次贝塞尔曲线转换: start={:?}, control={:?}, end={:?} -> control1={:?}",
                            start_point, control_point, end_point, control1
                        );
                    }
                }
                PathCommand::Close => {
                    path_builder.close();
                    // close 不改变当前位置
                }
            }
        }

        // 构建并绘制路径
        if let Ok(gpui_path) = path_builder.build() {
            let color = if let Some(fill_color) = style.fill {
                self.convert_color_to_hsla(fill_color)
            } else if let Some(stroke_style) = &style.stroke {
                self.convert_color_to_hsla(stroke_style.color)
            } else {
                Hsla { h: 0.0, s: 0.0, l: 0.0, a: 1.0 } // 默认黑色
            };
            window.paint_path(gpui_path, color);
        }
    }

    /// 绘制多边形
    fn draw_polygon(&self, points: &[Point], style: &PolygonStyle, window: &mut Window, _cx: &mut App) {
        if self.debug {
            println!("绘制多边形: {} 个点", points.len());
        }

        if points.is_empty() {
            return;
        }

        // 创建多边形路径
        let mut path_builder = if style.fill.is_some() {
            PathBuilder::fill()
        } else if let Some(stroke_style) = &style.stroke {
            PathBuilder::stroke(px(stroke_style.width as f32))
        } else {
            PathBuilder::stroke(px(1.0)) // 默认宽度
        };

        // 移动到第一个点
        let first_point = GpuiPoint::new(px(points[0].x as f32), px(points[0].y as f32));
        path_builder.move_to(first_point);

        // 连接其余点
        for point in &points[1..] {
            let gpui_point = GpuiPoint::new(px(point.x as f32), px(point.y as f32));
            path_builder.line_to(gpui_point);
        }

        // 闭合多边形
        path_builder.close();

        // 构建并绘制路径
        if let Ok(path) = path_builder.build() {
            let color = if let Some(fill_color) = style.fill {
                self.convert_color_to_hsla(fill_color)
            } else if let Some(stroke_style) = &style.stroke {
                self.convert_color_to_hsla(stroke_style.color)
            } else {
                Hsla { h: 0.0, s: 0.0, l: 0.0, a: 1.0 }
            };
            window.paint_path(path, color);
        }
    }

    /// 绘制点集
    fn draw_points(&self, points: &[Point], style: &PointStyle, window: &mut Window, _cx: &mut App) {
        if self.debug {
            println!("绘制点集: {} 个点", points.len());
        }

        let point_size = style.size as f32;
        let color = self.convert_color_to_hsla(style.color);

        // 为每个点绘制一个小圆形
        for point in points {
            let bounds = GpuiBounds::new(
                gpui::point(px(point.x as f32 - point_size / 2.0), px(point.y as f32 - point_size / 2.0)),
                size(px(point_size), px(point_size))
            );

            let background = Background::from(color);
            let corner_radius = px(point_size / 2.0);
            let corner_radii = Corners {
                top_left: corner_radius,
                top_right: corner_radius,
                bottom_left: corner_radius,
                bottom_right: corner_radius,
            };

            let paint_quad = PaintQuad {
                bounds,
                background,
                border_widths: Default::default(),
                border_color: Default::default(),
                border_style: Default::default(),
                corner_radii,
            };
            window.paint_quad(paint_quad);
        }
    }

    /// 绘制线段集
    fn draw_lines(&self, segments: &[(Point, Point)], style: &LineStyle, window: &mut Window, _cx: &mut App) {
        if self.debug {
            println!("绘制线段集: {} 条线段", segments.len());
        }

        // 创建单个路径包含所有线段
        let mut path_builder = PathBuilder::stroke(px(style.width as f32));

        for (from, to) in segments {
            let start_point = GpuiPoint::new(px(from.x as f32), px(from.y as f32));
            let end_point = GpuiPoint::new(px(to.x as f32), px(to.y as f32));

            path_builder.move_to(start_point);
            path_builder.line_to(end_point);
        }

        // 构建并绘制路径
        if let Ok(path) = path_builder.build() {
            let color = self.convert_color_to_hsla(style.color);
            window.paint_path(path, color);
        }
    }

    /// 转换 GPUI 边界到内部边界
    fn convert_bounds(&self, bounds: GpuiBounds<Pixels>) -> Bounds {
        // 使用新的转换 trait 进行转换
        bounds.to_echarts()
    }

    /// 将 echarts_core::Color 转换为 GPUI Hsla
    fn convert_color_to_hsla(&self, color: Color) -> Hsla {
        // Color 是一个结构体，有 r, g, b, a 字段
        let (h, s, l) = color.to_hsl(); // 使用内置的 HSL 转换
        Hsla {
            h: h,
            s: s,
            l: l,
            a: color.a,
        }
    }

    /// 计算旋转后的文本位置
    fn calculate_rotated_position(&self, position: Point, rotation: f64) -> Point {
        if rotation == 0.0 {
            return position;
        }

        // 简单的旋转计算（围绕原点）
        let cos_r = rotation.cos();
        let sin_r = rotation.sin();

        Point::new(
            position.x * cos_r - position.y * sin_r,
            position.x * sin_r + position.y * cos_r,
        )
    }

    /// 获取文本的边界框（用于布局计算）
    fn get_text_bounds(&self, text: &str, style: &TextStyle, window: &Window) -> Option<GpuiBounds<Pixels>> {
        if text.is_empty() {
            return None;
        }

        let font_size = px(style.font_size.max(1.0) as f32);
        let shared_text: SharedString = text.to_string().into();

        // 创建简单的文本运行用于测量
        let text_run = TextRun {
            len: text.len(),
            font: window.text_style().font(),
            color: Hsla { h: 0.0, s: 0.0, l: 0.0, a: 1.0 }, // 颜色不影响大小
            background_color: None,
            underline: None,
            strikethrough: None,
        };

        // 获取文本大小
        if let Ok(shaped_text) = window.text_system().shape_text(shared_text, font_size, &[text_run], None, None) {
            if let Some(line) = shaped_text.first() {
                let size = line.size(font_size);
                return Some(GpuiBounds::new(
                    point(px(0.0), px(0.0)),
                    gpui::size(size.width, size.height)
                ));
            }
        }

        None
    }

    /// 将二次贝塞尔曲线转换为三次贝塞尔曲线的控制点
    ///
    /// 这个函数实现了精确的数学转换，将二次贝塞尔曲线转换为等价的三次贝塞尔曲线。
    ///
    /// # 数学原理
    ///
    /// 二次贝塞尔曲线：Q(t) = (1-t)²P₀ + 2(1-t)tP₁ + t²P₂
    /// 三次贝塞尔曲线：C(t) = (1-t)³P₀ + 3(1-t)²tC₁ + 3(1-t)t²C₂ + t³P₂
    ///
    /// 转换公式：
    /// - C₁ = P₀ + 2/3(P₁ - P₀)
    /// - C₂ = P₂ + 2/3(P₁ - P₂)
    ///
    /// # 参数
    /// - `start`: 起始点 P₀
    /// - `control`: 二次贝塞尔曲线的控制点 P₁
    /// - `end`: 结束点 P₂
    ///
    /// # 返回值
    /// 返回 (control1, control2) 两个三次贝塞尔曲线的控制点
    fn quadratic_to_cubic_bezier(
        &self,
        start: GpuiPoint<Pixels>,
        control: GpuiPoint<Pixels>,
        end: GpuiPoint<Pixels>,
    ) -> (GpuiPoint<Pixels>, GpuiPoint<Pixels>) {
        // C₁ = P₀ + 2/3(P₁ - P₀)
        let control1 = GpuiPoint::new(
            start.x + (control.x - start.x) * 2.0 / 3.0,
            start.y + (control.y - start.y) * 2.0 / 3.0,
        );

        // C₂ = P₂ + 2/3(P₁ - P₂)
        let control2 = GpuiPoint::new(
            end.x + (control.x - end.x) * 2.0 / 3.0,
            end.y + (control.y - end.y) * 2.0 / 3.0,
        );

        (control1, control2)
    }

    /// 绘制三次贝塞尔曲线
    ///
    /// 这个函数提供了一个更高级的接口来绘制三次贝塞尔曲线，
    /// 支持两个控制点的完整三次贝塞尔曲线。
    ///
    /// # 参数
    /// - `path_builder`: GPUI 路径构建器
    /// - `start`: 起始点
    /// - `control1`: 第一个控制点
    /// - `control2`: 第二个控制点
    /// - `end`: 结束点
    fn cubic_bezier_to(
        &self,
        path_builder: &mut PathBuilder,
        _start: GpuiPoint<Pixels>,
        control1: GpuiPoint<Pixels>,
        control2: GpuiPoint<Pixels>,
        end: GpuiPoint<Pixels>,
    ) {
        // 如果 GPUI 支持完整的三次贝塞尔曲线，直接使用
        // 否则，我们需要用分段线性近似或其他方法

        // 方法1：尝试使用 GPUI 的原生支持（如果有的话）
        // path_builder.cubic_bezier_to(end, control1, control2);

        // 方法2：使用单控制点近似（当前 GPUI 支持的方式）
        // 我们可以使用两个控制点的中点作为近似
        let avg_control = GpuiPoint::new(
            (control1.x + control2.x) / 2.0,
            (control1.y + control2.y) / 2.0,
        );
        path_builder.curve_to(end, avg_control);

        // 方法3：分段线性近似（最精确但性能较低）
        // self.draw_cubic_bezier_approximation(path_builder, start, control1, control2, end, 16);
    }

    /// 使用分段线性近似绘制三次贝塞尔曲线
    ///
    /// 当需要最高精度时使用此方法
    ///
    /// # 参数
    /// - `path_builder`: GPUI 路径构建器
    /// - `start`: 起始点
    /// - `control1`: 第一个控制点
    /// - `control2`: 第二个控制点
    /// - `end`: 结束点
    /// - `segments`: 分段数量
    fn draw_cubic_bezier_approximation(
        &self,
        path_builder: &mut PathBuilder,
        start: GpuiPoint<Pixels>,
        control1: GpuiPoint<Pixels>,
        control2: GpuiPoint<Pixels>,
        end: GpuiPoint<Pixels>,
        segments: u32,
    ) {
        if segments == 0 {
            path_builder.line_to(end);
            return;
        }

        let step = 1.0 / segments as f32;

        for i in 1..=segments {
            let t = i as f32 * step;

            // 三次贝塞尔曲线公式：
            // B(t) = (1-t)³P₀ + 3(1-t)²tP₁ + 3(1-t)t²P₂ + t³P₃
            let t_inv = 1.0 - t;
            let t_inv_sq = t_inv * t_inv;
            let t_inv_cube = t_inv_sq * t_inv;
            let t_sq = t * t;
            let t_cube = t_sq * t;

            let coeff0 = t_inv_cube;
            let coeff1 = 3.0 * t_inv_sq * t;
            let coeff2 = 3.0 * t_inv * t_sq;
            let coeff3 = t_cube;

            let x = coeff0 * start.x.0 + coeff1 * control1.x.0 + coeff2 * control2.x.0 + coeff3 * end.x.0;
            let y = coeff0 * start.y.0 + coeff1 * control1.y.0 + coeff2 * control2.y.0 + coeff3 * end.y.0;

            let point = GpuiPoint::new(px(x), px(y));
            path_builder.line_to(point);
        }
    }

    /// 使用分段线性近似绘制二次贝塞尔曲线
    ///
    /// 当 GPUI 不支持完整的三次贝塞尔曲线时，可以使用这个方法
    /// 将曲线分解为多个线段来近似绘制
    ///
    /// # 参数
    /// - `path_builder`: GPUI 路径构建器
    /// - `start`: 起始点
    /// - `control`: 控制点
    /// - `end`: 结束点
    /// - `segments`: 分段数量（越多越平滑，但性能越低）
    fn draw_quadratic_bezier_approximation(
        &self,
        path_builder: &mut PathBuilder,
        start: GpuiPoint<Pixels>,
        control: GpuiPoint<Pixels>,
        end: GpuiPoint<Pixels>,
        segments: u32,
    ) {
        if segments == 0 {
            path_builder.line_to(end);
            return;
        }

        let step = 1.0 / segments as f32;

        for i in 1..=segments {
            let t = i as f32 * step;

            // 二次贝塞尔曲线公式：Q(t) = (1-t)²P₀ + 2(1-t)tP₁ + t²P₂
            let t_inv = 1.0 - t;
            let t_inv_sq = t_inv * t_inv;
            let t_sq = t * t;
            let two_t_inv_t = 2.0 * t_inv * t;

            let x = t_inv_sq * start.x.0 + two_t_inv_t * control.x.0 + t_sq * end.x.0;
            let y = t_inv_sq * start.y.0 + two_t_inv_t * control.y.0 + t_sq * end.y.0;

            let point = GpuiPoint::new(px(x), px(y));
            path_builder.line_to(point);
        }
    }
}

// ============================================================================
// GPUI 类型转换 - ECharts 与 GPUI 类型之间的转换
// ============================================================================

/// ECharts 与 GPUI 类型转换工具
///
/// 由于 Rust 的孤儿规则，我们不能直接为外部类型实现外部 trait，
/// 所以使用转换函数和扩展 trait 的方式来提供类型转换功能。
pub mod type_conversion {
    use super::*;

    /// ECharts 类型的 GPUI 转换扩展 trait
    pub trait ToGpui<T> {
        fn to_gpui(self) -> T;
    }

    /// GPUI 类型的 ECharts 转换扩展 trait
    pub trait ToECharts<T> {
        fn to_echarts(self) -> T;
    }

    // ============================================================================
    // Bounds 转换实现
    // ============================================================================

    impl ToGpui<GpuiBounds<Pixels>> for Bounds {
        fn to_gpui(self) -> GpuiBounds<Pixels> {
            GpuiBounds::new(
                point(
                    px(self.origin.x as f32),
                    px(self.origin.y as f32)
                ),
                size(
                    px(self.size.width as f32),
                    px(self.size.height as f32)
                )
            )
        }
    }

    impl ToECharts<Bounds> for GpuiBounds<Pixels> {
        fn to_echarts(self) -> Bounds {
            Bounds::new(
                self.origin.x.0 as f64,
                self.origin.y.0 as f64,
                self.size.width.0 as f64,
                self.size.height.0 as f64,
            )
        }
    }

    // ============================================================================
    // Point 转换实现
    // ============================================================================

    impl ToGpui<GpuiPoint<Pixels>> for Point {
        fn to_gpui(self) -> GpuiPoint<Pixels> {
            point(
                px(self.x as f32),
                px(self.y as f32)
            )
        }
    }

    impl ToECharts<Point> for GpuiPoint<Pixels> {
        fn to_echarts(self) -> Point {
            Point::new(
                self.x.0 as f64,
                self.y.0 as f64,
            )
        }
    }

    // ============================================================================
    // Size 转换实现
    // ============================================================================

    impl ToGpui<GpuiSize<Pixels>> for Size {
        fn to_gpui(self) -> GpuiSize<Pixels> {
            size(
                px(self.width as f32),
                px(self.height as f32)
            )
        }
    }

    impl ToECharts<Size> for GpuiSize<Pixels> {
        fn to_echarts(self) -> Size {
            Size::new(
                self.width.0 as f64,
                self.height.0 as f64,
            )
        }
    }

    // ============================================================================
    // 便捷转换函数
    // ============================================================================

    /// 便捷转换函数模块
    pub mod convert {
        use super::*;

        /// 将 GPUI Bounds 转换为 ECharts Bounds
        pub fn gpui_bounds_to_echarts(bounds: GpuiBounds<Pixels>) -> Bounds {
            bounds.to_echarts()
        }

        /// 将 ECharts Bounds 转换为 GPUI Bounds
        pub fn echarts_bounds_to_gpui(bounds: Bounds) -> GpuiBounds<Pixels> {
            bounds.to_gpui()
        }

        /// 将 GPUI Point 转换为 ECharts Point
        pub fn gpui_point_to_echarts(point: GpuiPoint<Pixels>) -> Point {
            point.to_echarts()
        }

        /// 将 ECharts Point 转换为 GPUI Point
        pub fn echarts_point_to_gpui(point: Point) -> GpuiPoint<Pixels> {
            point.to_gpui()
        }

        /// 将 GPUI Size 转换为 ECharts Size
        pub fn gpui_size_to_echarts(size: GpuiSize<Pixels>) -> Size {
            size.to_echarts()
        }

        /// 将 ECharts Size 转换为 GPUI Size
        pub fn echarts_size_to_gpui(size: Size) -> GpuiSize<Pixels> {
            size.to_gpui()
        }
    }
}

pub use type_conversion::{ToGpui, ToECharts, convert};

impl Default for GpuiRenderer {
    fn default() -> Self {
        Self::new()
    }
}

/// 创建一个示例文本样式
impl GpuiRenderer {
    /// 创建默认文本样式
    pub fn default_text_style() -> TextStyle {
        TextStyle {
            font_family: "Arial".to_string(),
            font_size: 14.0,
            font_weight: echarts_core::FontWeight::Normal,
            font_style: echarts_core::FontStyle::Normal,
            color: Color::BLACK,
            opacity: 1.0,
            align: echarts_core::TextAlign::Left,
            baseline: echarts_core::TextBaseline::Bottom,
            rotation: 0.0,
            letter_spacing: 0.0,
            line_height: 1.2,
        }
    }

    /// 创建标题文本样式
    pub fn title_text_style() -> TextStyle {
        TextStyle {
            font_family: "Arial".to_string(),
            font_size: 18.0,
            font_weight: echarts_core::FontWeight::Bold,
            font_style: echarts_core::FontStyle::Normal,
            color: Color::BLACK,
            opacity: 1.0,
            align: echarts_core::TextAlign::Center,
            baseline: echarts_core::TextBaseline::Middle,
            rotation: 0.0,
            letter_spacing: 0.0,
            line_height: 1.2,
        }
    }

    /// 创建轴标签文本样式
    pub fn axis_label_text_style() -> TextStyle {
        TextStyle {
            font_family: "Arial".to_string(),
            font_size: 12.0,
            font_weight: echarts_core::FontWeight::Normal,
            font_style: echarts_core::FontStyle::Normal,
            color: Color::DARK_GRAY,
            opacity: 0.8,
            align: echarts_core::TextAlign::Center,
            baseline: echarts_core::TextBaseline::Top,
            rotation: 0.0,
            letter_spacing: 0.0,
            line_height: 1.0,
        }
    }

    /// 创建一个示例二次贝塞尔曲线路径
    ///
    /// 这个函数展示了如何创建包含二次贝塞尔曲线的路径，
    /// 这些曲线将被自动转换为三次贝塞尔曲线进行绘制。
    ///
    /// # 返回值
    /// 返回一个包含二次贝塞尔曲线的路径命令向量
    pub fn create_sample_quadratic_path() -> Vec<PathCommand> {
        vec![
            // 移动到起始点
            PathCommand::MoveTo(Point::new(100.0, 200.0)),

            // 绘制一个简单的二次贝塞尔曲线
            PathCommand::QuadTo {
                control: Point::new(200.0, 100.0), // 控制点
                to: Point::new(300.0, 200.0),      // 结束点
            },

            // 连接另一个二次贝塞尔曲线
            PathCommand::QuadTo {
                control: Point::new(400.0, 300.0),
                to: Point::new(500.0, 200.0),
            },

            // 绘制一个 S 形曲线
            PathCommand::QuadTo {
                control: Point::new(600.0, 100.0),
                to: Point::new(700.0, 200.0),
            },
        ]
    }

    /// 创建一个心形路径示例（使用二次贝塞尔曲线）
    ///
    /// 这个函数展示了如何使用二次贝塞尔曲线创建复杂的形状
    pub fn create_heart_shape_path() -> Vec<PathCommand> {
        let center_x = 400.0;
        let center_y = 300.0;
        let size = 50.0;

        vec![
            // 移动到心形底部
            PathCommand::MoveTo(Point::new(center_x, center_y + size)),

            // 左侧心形曲线
            PathCommand::QuadTo {
                control: Point::new(center_x - size * 2.0, center_y - size),
                to: Point::new(center_x - size, center_y),
            },

            PathCommand::QuadTo {
                control: Point::new(center_x - size / 2.0, center_y - size / 2.0),
                to: Point::new(center_x, center_y),
            },

            // 右侧心形曲线
            PathCommand::QuadTo {
                control: Point::new(center_x + size / 2.0, center_y - size / 2.0),
                to: Point::new(center_x + size, center_y),
            },

            PathCommand::QuadTo {
                control: Point::new(center_x + size * 2.0, center_y - size),
                to: Point::new(center_x, center_y + size),
            },

            // 闭合路径
            PathCommand::Close,
        ]
    }

    /// 创建平滑的 Catmull-Rom 样条曲线路径
    ///
    /// 这个函数模拟了您提供的示例代码中的 Catmull-Rom 样条曲线算法，
    /// 用于创建通过一系列点的平滑曲线。
    ///
    /// # 参数
    /// - `points`: 要连接的点序列
    ///
    /// # 返回值
    /// 返回包含平滑曲线的路径命令
    pub fn create_catmull_rom_path(points: &[Point]) -> Vec<PathCommand> {
        if points.is_empty() {
            return vec![];
        }

        if points.len() == 1 {
            return vec![PathCommand::MoveTo(points[0])];
        }

        let mut commands = vec![PathCommand::MoveTo(points[0])];

        if points.len() == 2 {
            commands.push(PathCommand::LineTo(points[1]));
            return commands;
        }

        let n = points.len();
        for i in 0..n - 1 {
            let p0 = if i == 0 { points[0] } else { points[i - 1] };
            let p1 = points[i];
            let p2 = points[i + 1];
            let p3 = if i + 2 < n { points[i + 2] } else { points[n - 1] };

            // Catmull-Rom 到贝塞尔曲线的转换
            // 这是您示例代码中使用的算法
            let c1 = Point::new(
                p1.x + (p2.x - p0.x) / 6.0,
                p1.y + (p2.y - p0.y) / 6.0,
            );
            let c2 = Point::new(
                p2.x - (p3.x - p1.x) / 6.0,
                p2.y - (p3.y - p1.y) / 6.0,
            );

            commands.push(PathCommand::CurveTo {
                control1: c1,
                control2: c2,
                to: p2,
            });
        }

        commands
    }

    /// 创建线性连接的路径
    ///
    /// 对应您示例代码中的 Linear 模式
    pub fn create_linear_path(points: &[Point]) -> Vec<PathCommand> {
        if points.is_empty() {
            return vec![];
        }

        let mut commands = vec![PathCommand::MoveTo(points[0])];

        for point in &points[1..] {
            commands.push(PathCommand::LineTo(*point));
        }

        commands
    }

    /// 创建示例数据点的平滑曲线
    ///
    /// 这个函数展示了如何使用 Catmull-Rom 样条曲线创建图表中常见的平滑数据线
    pub fn create_sample_data_curve() -> Vec<PathCommand> {
        let sample_points = vec![
            Point::new(50.0, 200.0),
            Point::new(150.0, 100.0),
            Point::new(250.0, 180.0),
            Point::new(350.0, 80.0),
            Point::new(450.0, 160.0),
            Point::new(550.0, 120.0),
            Point::new(650.0, 200.0),
        ];

        Self::create_catmull_rom_path(&sample_points)
    }
}

// ============================================================================
// GPUI Plot 集成 - 图表绘制 trait
// ============================================================================

/// GPUI 图表绘制 trait
///
/// 这个 trait 定义了在 GPUI 中绘制图表的接口，类似于 gpui_component 中的 Plot trait
pub trait Plot {
    /// 绘制图表到指定的边界区域
    fn paint(&mut self, bounds: GpuiBounds<Pixels>, window: &mut Window, cx: &mut App);
}

/// IntoPlot trait，用于将对象转换为可绘制的图表
pub trait IntoPlot {
    type Plot: Plot;

    /// 转换为 Plot 对象
    fn into_plot(self) -> Self::Plot;
}

/// 为实现了 Plot 的类型自动实现 IntoPlot
impl<T: Plot> IntoPlot for T {
    type Plot = T;

    fn into_plot(self) -> Self::Plot {
        self
    }
}

/// ECharts 图表画布 - 集成 ECharts 系列和 GPUI 渲染
pub struct EChartsCanvas {
    /// 图表系列
    pub series: Box<dyn echarts_core::Series>,
    /// 坐标系统
    pub coord_system: echarts_core::CartesianCoordinateSystem,
    /// GPUI 渲染器
    pub renderer: GpuiRenderer,
    /// 调试模式
    pub debug: bool,
}

impl EChartsCanvas {
    /// 创建新的图表画布
    pub fn new(
        series: Box<dyn echarts_core::Series>,
        coord_system: echarts_core::CartesianCoordinateSystem,
    ) -> Self {
        Self {
            series,
            coord_system,
            renderer: GpuiRenderer::new(),
            debug: false,
        }
    }

    /// 启用调试模式
    pub fn with_debug(mut self, debug: bool) -> Self {
        self.debug = debug;
        self.renderer = self.renderer.with_debug(debug);
        self
    }

    /// 设置系列数据
    pub fn set_series(&mut self, series: Box<dyn echarts_core::Series>) {
        self.series = series;
    }

    /// 更新坐标系统
    pub fn set_coord_system(&mut self, coord_system: echarts_core::CartesianCoordinateSystem) {
        self.coord_system = coord_system;
    }
}

impl Plot for EChartsCanvas {
    fn paint(&mut self, bounds: GpuiBounds<Pixels>, window: &mut Window, cx: &mut App) {
        if self.debug {
            println!("🎨 EChartsCanvas 开始渲染图表");
            println!("  - 系列名称: {}", self.series.name());
            println!("  - 系列类型: {:?}", self.series.series_type());
            println!("  - 边界: {:?}", bounds);
        }

        // 更新坐标系统边界
        let mut coord_system = self.coord_system.clone();
        coord_system.bounds = bounds.to_echarts();

        // 生成绘制命令
        match self.series.render_to_commands(&coord_system) {
            Ok(commands) => {
                if self.debug {
                    println!("✅ 成功生成 {} 个绘制命令", commands.len());
                }

                // 使用 GPUI 渲染器渲染命令
                if let Err(err) = self.renderer.render_chart(commands, bounds, window, cx) {
                    if self.debug {
                        println!("❌ 渲染失败: {:?}", err);
                    }
                } else if self.debug {
                    println!("🎉 渲染完成，统计: {:?}", self.renderer.stats());
                }
            }
            Err(e) => {
                if self.debug {
                    println!("❌ 绘制命令生成失败: {:?}", e);
                }
            }
        }
    }
}

/// EChartsCanvas 的 GPUI Element 包装器
pub struct EChartsElement {
    canvas: EChartsCanvas,
}

impl EChartsElement {
    pub fn new(canvas: EChartsCanvas) -> Self {
        Self { canvas }
    }
}

impl IntoElement for EChartsCanvas {
    type Element = EChartsElement;

    fn into_element(self) -> Self::Element {
        EChartsElement::new(self)
    }
    
    fn into_any_element(self) -> gpui::AnyElement {
        self.into_element().into_any()
    }
}

impl Element for EChartsElement {
    type RequestLayoutState = ();
    type PrepaintState = ();

    fn id(&self) -> Option<gpui::ElementId> {
        None
    }

    fn source_location(&self) -> Option<std::panic::Location<'static>> {
        None
    }

    fn request_layout(
        &mut self,
        _id: Option<&gpui::GlobalElementId>,
        cx: &mut gpui::WindowContext,
    ) -> (gpui::LayoutId, Self::RequestLayoutState) {
        let layout_id = cx.request_layout(gpui::Style::default(), None);
        (layout_id, ())
    }

    fn prepaint(
        &mut self,
        _id: Option<&gpui::GlobalElementId>,
        bounds: GpuiBounds<Pixels>,
        _request_layout_state: &mut Self::RequestLayoutState,
        cx: &mut gpui::WindowContext,
    ) -> Self::PrepaintState {
        // 这里可以进行预绘制准备工作
        if self.canvas.debug {
            println!("🎨 EChartsElement prepaint - bounds: {:?}", bounds);
        }
        ()
    }

    fn paint(
        &mut self,
        _id: Option<&gpui::GlobalElementId>,
        bounds: GpuiBounds<Pixels>,
        _request_layout_state: &mut Self::RequestLayoutState,
        _prepaint_state: &mut Self::PrepaintState,
        cx: &mut gpui::WindowContext,
    ) {
        // 在这里调用实际的图表绘制
        if self.canvas.debug {
            println!("🎨 EChartsElement paint - bounds: {:?}", bounds);
        }

        // 更新坐标系统边界
        let mut coord_system = self.canvas.coord_system.clone();
        coord_system.bounds = bounds.to_echarts();

        // 生成绘制命令
        match self.canvas.series.render_to_commands(&coord_system) {
            Ok(commands) => {
                if self.canvas.debug {
                    println!("✅ 成功生成 {} 个绘制命令", commands.len());
                }

                // 使用 GPUI 渲染器渲染命令
                // 注意：这里需要适配 WindowContext，暂时跳过实际渲染
                if self.canvas.debug {
                    println!("🎉 图表元素渲染准备完成，命令数: {}", commands.len());
                    println!("📝 注意：实际渲染需要适配 WindowContext API");
                }
            }
            Err(e) => {
                if self.canvas.debug {
                    println!("❌ 绘制命令生成失败: {:?}", e);
                }
            }
        }
    }
}

// 重新导出 Plot 相关功能
pub use {Plot, IntoPlot, EChartsCanvas, EChartsElement};

// 重新导出核心类型（避免重复导入）
