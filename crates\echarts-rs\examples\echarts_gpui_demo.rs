//! ECharts with GPUI Renderer Demo
//!
//! This example demonstrates how to use ECharts library with GPUI renderer
//! to display charts in a GPUI application.

use echarts_rs::{
    BarSeries, Bounds as EchartsBounds, CartesianCoordinateSystem, Color, LineSeries,
    ScatterSeries, Series,
};
use gpui::*;
use gpui_component::{
    plot::{IntoPlot, Plot},
    StyledExt,
};
use gpui_renderer::{<PERSON><PERSON>i<PERSON><PERSON><PERSON>, ToECharts as _};
// use gpui_component::IntoPlot; // 此 trait 未找到，注释掉以修复编译错误

fn main() {
    println!("🚀 启动 ECharts + GPUI 渲染器演示...");

    let app = Application::new();

    app.run(move |cx| {
        println!("📱 应用程序上下文已创建");

        // 创建窗口
        let mut window_size = size(px(1200.0), px(800.0));
        if let Some(display) = cx.primary_display() {
            let display_size = display.bounds().size;
            window_size.width = window_size.width.min(display_size.width * 0.9);
            window_size.height = window_size.height.min(display_size.height * 0.9);
            println!(
                "🖥️  显示器大小: {:?}, 窗口大小: {:?}",
                display_size, window_size
            );
        }
        let window_bounds = gpui::Bounds::centered(None, window_size, cx);

        println!("🪟 准备创建窗口...");

        cx.spawn(async move |cx| {
            let options = WindowOptions {
                window_bounds: Some(WindowBounds::Windowed(window_bounds)),
                titlebar: Some(TitlebarOptions {
                    title: Some("ECharts + GPUI 渲染器演示".into()),
                    appears_transparent: false,
                    traffic_light_position: None,
                }),
                window_min_size: Some(size(px(800.0), px(600.0))),
                focus: true,
                show: true,
                kind: WindowKind::Normal,
                is_movable: true,
                display_id: None,
                app_id: None,
                window_background: WindowBackgroundAppearance::Opaque,
                window_decorations: None,
            };

            match cx.open_window(options, |window, cx| {
                println!("✅ 窗口已创建，正在初始化 ECharts 演示...");
                cx.new(|cx| EChartsGpuiDemo::new(window, cx))
            }) {
                Ok(_) => println!("🎉 窗口创建成功！"),
                Err(e) => println!("❌ 窗口创建失败: {:?}", e),
            }
        })
        .detach();

        println!("⏳ 等待窗口显示...");
    });

    println!("👋 应用程序退出");
}

/// ECharts GPUI 演示应用
struct EChartsGpuiDemo {
    /// 当前图表类型
    chart_type: ChartType,
    /// 当前系列数据
    current_series: Box<dyn Series>,
    /// 坐标系统
    coord_system: CartesianCoordinateSystem,
}

#[derive(Debug, Clone, PartialEq)]
enum ChartType {
    LineChart,
    BarChart,
    ScatterChart,
}

impl ChartType {
    fn name(&self) -> &'static str {
        match self {
            ChartType::LineChart => "折线图",
            ChartType::BarChart => "柱状图",
            ChartType::ScatterChart => "散点图",
        }
    }
}

impl EChartsGpuiDemo {
    fn new(_window: &mut Window, _cx: &mut Context<Self>) -> Self {
        println!("🎯 初始化 ECharts 演示...");

        // 创建坐标系统
        let coord_system = CartesianCoordinateSystem::new(
            EchartsBounds::new(50.0, 50.0, 600.0, 400.0),
            (0.0, 10.0),
            (0.0, 100.0),
        );

        // 创建默认线图系列
        let current_series = Self::create_line_series();
        println!("📊 ECharts 系列已创建");

        Self {
            chart_type: ChartType::LineChart,
            current_series,
            coord_system,
        }
    }

    /// 创建折线图系列
    fn create_line_series() -> Box<dyn Series> {
        println!("📈 创建折线图系列...");

        // 创建示例数据
        let data: Vec<(f64, f64)> = (0..10)
            .map(|i| {
                let x = i as f64;
                let y = 50.0 + 30.0 * (x * 0.5).sin() + 10.0 * (x * 1.2).cos();
                (x, y)
            })
            .collect();

        println!("📊 生成数据点: {} 个", data.len());

        // 创建线图系列
        let line_series = LineSeries::new("示例数据")
            .data(data)
            .smooth(true)
            .color(Color::rgb(0.33, 0.44, 0.78));

        Box::new(line_series)
    }

    /// 创建柱状图系列
    fn create_bar_series() -> Box<dyn Series> {
        println!("📊 创建柱状图系列...");

        // 创建示例数据
        let data: Vec<(f64, f64)> = vec![
            (1.0, 65.0),
            (2.0, 78.0),
            (3.0, 45.0),
            (4.0, 92.0),
            (5.0, 58.0),
            (6.0, 83.0),
        ];

        println!("📊 生成数据点: {} 个", data.len());

        // 创建柱图系列
        let bar_series = BarSeries::new("月度数据")
            .data(data)
            .color(Color::rgb(0.57, 0.8, 0.46))
            .bar_width(0.6);

        Box::new(bar_series)
    }

    /// 创建散点图系列
    fn create_scatter_series() -> Box<dyn Series> {
        println!("🎯 创建散点图系列...");

        // 创建示例数据
        let data: Vec<(f64, f64)> = (0..20)
            .map(|i| {
                let x = i as f64;
                let y = 30.0 + 20.0 * (x * 0.1).sin() + (i as f64 % 3.0) * 10.0;
                (x, y)
            })
            .collect();

        println!("📊 生成数据点: {} 个", data.len());

        // 创建散点图系列
        let scatter_series = ScatterSeries::new("散点数据")
            .data(data)
            .symbol_size(6.0)
            .color(Color::rgb(0.98, 0.78, 0.35));

        Box::new(scatter_series)
    }
}

impl Render for EChartsGpuiDemo {
    fn render(&mut self, _window: &mut Window, _cx: &mut Context<Self>) -> impl IntoElement {
        println!("🎨 渲染 ECharts 演示界面...");

        div()
            .size_full()
            .bg(rgb(0xf8f9fa))
            .flex()
            .flex_col()
            .child(self.render_header())
            .child(self.render_chart_area())
    }
}

impl EChartsGpuiDemo {
    /// 渲染头部区域
    fn render_header(&self) -> impl IntoElement {
        div()
            .w_full()
            .px_6()
            .py_4()
            .bg(rgb(0xffffff))
            .border_b_1()
            .border_color(rgb(0xe5e7eb))
            .flex()
            .items_center()
            .justify_between()
            .child(
                // 标题
                div()
                    .text_xl()
                    .font_semibold()
                    .text_color(rgb(0x1f2937))
                    .child("ECharts + GPUI 渲染器演示"),
            )
            .child(
                // 控制按钮
                div()
                    .flex()
                    .items_center()
                    .gap_4()
                    .child(
                        // 图表类型切换按钮
                        div().flex().gap_2().children([
                            self.render_chart_type_button(ChartType::LineChart),
                            self.render_chart_type_button(ChartType::BarChart),
                            self.render_chart_type_button(ChartType::ScatterChart),
                        ]),
                    )
                    .child(
                        // 信息显示
                        div()
                            .px_4()
                            .py_2()
                            .bg(rgb(0x6b7280))
                            .text_color(rgb(0xffffff))
                            .rounded_lg()
                            .child(format!("当前: {}", self.chart_type.name())),
                    ),
            )
    }

    /// 渲染图表类型按钮
    fn render_chart_type_button(&self, chart_type: ChartType) -> impl IntoElement {
        let is_active = self.chart_type == chart_type;
        let (bg_color, text_color, icon) = if is_active {
            (rgb(0x3b82f6), rgb(0xffffff), "●")
        } else {
            (rgb(0xf3f4f6), rgb(0x374151), "○")
        };

        div()
            .px_3()
            .py_2()
            .bg(bg_color)
            .text_color(text_color)
            .rounded_md()
            .cursor_pointer()
            .hover(|div| {
                if is_active {
                    div.bg(rgb(0x2563eb))
                } else {
                    div.bg(rgb(0xe5e7eb))
                }
            })
            .child(format!("{} {}", icon, chart_type.name()))
    }

    /// 切换图表类型
    fn switch_chart_type(&mut self, new_type: ChartType) {
        println!("🔄 切换图表类型: {:?} -> {:?}", self.chart_type, new_type);
        self.chart_type = new_type.clone();

        // 重新创建系列
        self.current_series = match new_type {
            ChartType::LineChart => Self::create_line_series(),
            ChartType::BarChart => Self::create_bar_series(),
            ChartType::ScatterChart => Self::create_scatter_series(),
        };

        // 更新坐标系统范围
        self.coord_system = match new_type {
            ChartType::LineChart => CartesianCoordinateSystem::new(
                EchartsBounds::new(50.0, 50.0, 600.0, 400.0),
                (0.0, 10.0),
                (0.0, 100.0),
            ),
            ChartType::BarChart => CartesianCoordinateSystem::new(
                EchartsBounds::new(50.0, 50.0, 600.0, 400.0),
                (0.0, 7.0),
                (0.0, 100.0),
            ),
            ChartType::ScatterChart => CartesianCoordinateSystem::new(
                EchartsBounds::new(50.0, 50.0, 600.0, 400.0),
                (0.0, 20.0),
                (0.0, 80.0),
            ),
        };

        println!("✅ 图表类型切换完成");
    }

    /// 渲染图表区域
    fn render_chart_area(&mut self) -> impl IntoElement {
        println!("📊 渲染图表区域...");

        div().flex_1().p_6().child(
            div()
                .size_full()
                .bg(rgb(0xffffff))
                .border_1()
                .border_color(rgb(0xe5e7eb))
                .rounded_lg()
                .shadow_sm()
                .child(
                    // 使用 Plot 组件来渲染 ECharts
                    self.render_plot(),
                ),
        )
    }

    /// 渲染 ECharts 图表
    fn render_plot(&self) -> impl IntoElement {
        println!("📊 渲染 ECharts 图表...");

        // 生成渲染命令
        let render_result = self.current_series.render_to_commands(&self.coord_system);

        div()
            .size_full()
            .flex()
            .items_center()
            .justify_center()
            .child(
                div()
                    .w(px(700.0))
                    .h(px(500.0))
                    .bg(rgb(0xffffff))
                    .border_1()
                    .border_color(rgb(0xe5e7eb))
                    .rounded_lg()
                    .p_4()
                    .child(
                        // 显示图表信息
                        div()
                            .size_full()
                            .flex()
                            .flex_col()
                            .items_center()
                            .justify_center()
                            .gap_4()
                            .child(
                                div()
                                    .text_xl()
                                    .font_semibold()
                                    .text_color(rgb(0x374151))
                                    .child(format!("📊 {}", self.chart_type.name())),
                            )
                            .child(div().text_sm().text_color(rgb(0x6b7280)).child(
                                match &render_result {
                                    Ok(commands) => {
                                        format!("✅ 渲染成功: {} 个绘制命令", commands.len())
                                    }
                                    Err(e) => format!("❌ 渲染失败: {:?}", e),
                                },
                            ))
                            .child(
                                div()
                                    .text_sm()
                                    .text_color(rgb(0x6b7280))
                                    .child("📊 ECharts-rs 图表系列"),
                            )
                            .child(
                                // 真实的图表渲染区域
                                div().w(px(600.0)).h(px(400.0)).child(ChartCanvas::new(
                                    self.current_series.clone_series(),
                                    self.coord_system.clone(),
                                    self.chart_type.clone(),
                                )),
                            ),
                    ),
            )
    }
}

/// 图表画布组件 - 负责实际渲染图表
#[derive(IntoPlot)]
struct ChartCanvas {
    series: Box<dyn Series>,
    coord_system: CartesianCoordinateSystem,
    chart_type: ChartType,
    renderer: GpuiRenderer,
}

impl ChartCanvas {
    fn new(
        series: Box<dyn Series>,
        coord_system: CartesianCoordinateSystem,
        chart_type: ChartType,
    ) -> Self {
        Self {
            series,
            coord_system,
            chart_type,
            renderer: GpuiRenderer::new().with_debug(true),
        }
    }
}

// 实现 Plot trait 以支持在 GPUI 中渲染
impl Plot for ChartCanvas {
    fn paint(&mut self, bounds: gpui::Bounds<gpui::Pixels>, window: &mut Window, cx: &mut App) {
        println!("🎨 ChartCanvas 开始渲染图表 - 类型: {:?}", self.chart_type);
        let mut coord_system = self.coord_system.clone();
        // 使用新的转换 trait 进行转换
        coord_system.bounds = bounds.to_echarts();
        // 生成绘制命令
        match self.series.render_to_commands(&coord_system) {
            Ok(commands) => {
                println!("✅ 成功生成 {} 个绘制命令", commands.len());

                // 使用 GPUI 渲染器渲染命令
                if let Err(err) = self.renderer.render_chart(commands, bounds, window, cx) {
                    println!("❌ 渲染失败: {:?}", err);
                } else {
                    println!("🎉 渲染完成，统计: {:?}", self.renderer.stats());
                }
            }
            Err(e) => {
                println!("❌ 绘制命令生成失败: {:?}", e);
            }
        }
    }
}

// impl IntoElement for ChartCanvas {
//     type Element = Div;

//     fn into_element(self) -> Self::Element {
//         div()
//             .w_full()
//             .h_full()
//             .bg(rgb(0xffffff))
//             .border_1()
//             .border_color(rgb(0xe5e7eb))
//             .rounded_lg()
//             .relative()
//     }
// }
