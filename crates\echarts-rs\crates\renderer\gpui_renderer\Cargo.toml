[package]
name = "gpui_renderer"
version = "0.1.0"
edition = "2021"
description = "GPUI renderer for Rust ECharts"
license = "Apache-2.0"

# 移除 workspace 声明，因为这是一个子包

[dependencies]
echarts-core = { path = "../../core", features = ["gpui"] }
echarts-themes = { path = "../../themes" }
echarts-charts = { path = "../../charts" }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
thiserror = "1.0"
anyhow = "1.0"

# Optional rendering backends
gpui = { workspace = true, optional = true }
lyon = { version = "1.0", optional = true }

[features]
default = ["gpui"]
gpui = ["dep:gpui"]
